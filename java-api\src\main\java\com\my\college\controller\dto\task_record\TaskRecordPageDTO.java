package com.my.college.controller.dto.task_record;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.alibaba.fastjson2.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.college.controller.vo.task_record.TaskRecordPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_任务记录表
 *
 * <AUTHOR>
 * @date 2025-05-03
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class TaskRecordPageDTO 
						extends PageDTO<TaskRecordPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务编号 
     */
    private String taskId;
    
    /**
     * 批次号
     */
    private Integer batchNumber;

    /**
     * 学校ID
     */
    private Integer schoolId;

    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    private LocalDateTime endTime;

    /**
     * 任务完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 任务状态(RUN, FAIL, SUCCESS)
     */
    private String status;
    
    /**
     * 任务失败备注
     */
    private String failRemark;

    /**
     * 请求参数
     */
    private String request;
    
    /**
     * 响应内容
     */
    private String response;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
