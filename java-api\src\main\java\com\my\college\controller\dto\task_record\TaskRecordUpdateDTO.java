package com.my.college.controller.dto.task_record;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_任务记录表
 *
 * <AUTHOR>
 * @date 2025-05-03
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class TaskRecordUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务编号 
     */
	@NotNull(message="taskId不能为空")
    private String taskId;

    /**
     * 批次号
     */
    private Integer batchNumber;

    /**
     * 学校ID
     */
    private Integer schoolId;

    /**
     * 任务开始时间
     */
	@NotNull(message="startTime不能为空")
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
	@NotNull(message="endTime不能为空")
    private LocalDateTime endTime;

    /**
     * 任务完成时间
     */
	@NotNull(message="finishTime不能为空")
    private LocalDateTime finishTime;

    /**
     * 任务状态(RUN, FAIL, SUCCESS)
     */
	@NotNull(message="status不能为空")
    private String status;
    
    /**
     * 任务失败备注
     */
    private String failRemark;

    /**
     * 请求参数(状态)
     */
	@NotNull(message="requestArgStatus不能为空")
    private String requestArgStatus;

    /**
     * 请求参数(策略)
     */
	@NotNull(message="requestArgStrategy不能为空")
    private String requestArgStrategy;

    /**
     * 请求参数(模板)
     */
	@NotNull(message="requestArgCmdTpl不能为空")
    private String requestArgCmdTpl;

    /**
     * 请求参数(用户提示词)
     */
    private String requestArgUserPrompt;

    /**
     * 响应内容(csv数据)
     */
	@NotNull(message="responseCsv不能为空")
    private String responseCsv;

    /**
     * 响应内容(推理详情)
     */
	@NotNull(message="responseReasoning不能为空")
    private String responseReasoning;

    /**
     * 创建时间
     */
	@NotNull(message="createTime不能为空")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
	@NotNull(message="updateTime不能为空")
    private LocalDateTime updateTime;

}
