package com.my.college.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 子任务记录表
 *
 * <AUTHOR>
 * @date 2025-06-22
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sub_task_record")
public class SubTaskRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 子任务编号
     */
    @TableId(value = "sub_task_id", type = IdType.INPUT)
    private String subTaskId;

    /**
     * 主任务编号
     */
    private String taskId;

    /**
     * 子任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 子任务结束时间
     */
    private LocalDateTime endTime;

    /**
     * 子任务完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 网址
     */
    private String url;

    /**
     * markdown请求
     */
    private String markdownRequest;

    /**
     * markdown响应
     */
    private String markdownResponse;

    /**
     * ai请求参数
     */
    private String aiRequest;
    
    /**
     * ai响应
     */
    private String aiResponse;

    /**
     * ai响应内容
     */
    private String aiContent;

    /**
     * ai推理详情
     */
    private String aiReasoning;

    /**
     * ai-token消耗情况
     */
    private String aiTokenUsage;

    /**
     * 子任务状态(RUN, FAIL, SUCCESS, REBORN)
     */
    private String status;

    /**
     * 子任务失败备注
     */
    private String failRemark;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;


}
