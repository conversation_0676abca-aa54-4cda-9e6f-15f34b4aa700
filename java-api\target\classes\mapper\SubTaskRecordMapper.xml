<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.college.mybatis.mapper.SubTaskRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.college.mybatis.entity.SubTaskRecord">
        <id column="sub_task_id" property="subTaskId" />
        <result column="task_id" property="taskId" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="finish_time" property="finishTime" />
        <result column="url" property="url" />
        <result column="markdown_request" property="markdownRequest" />
        <result column="markdown_response" property="markdownResponse" />
        <result column="ai_request" property="aiRequest" />
        <result column="ai_response" property="aiResponse" />
        <result column="ai_content" property="aiContent" />
        <result column="ai_reasoning" property="aiReasoning" />
        <result column="ai_token_usage" property="aiTokenUsage" />
        <result column="status" property="status" />
        <result column="fail_remark" property="failRemark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        sub_task_id, task_id, start_time, end_time, finish_time, url, markdown_request, markdown_response,
         ai_request, ai_response, ai_content, ai_reasoning, ai_token_usage, 
         status, fail_remark, create_time, update_time
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.college.controller.vo.sub_task_record.SubTaskRecordPageVO">
		SELECT
			sub_task_id, task_id, start_time, end_time, finish_time, url, markdown_request, markdown_response,
	         ai_request, ai_response, ai_content, ai_reasoning, ai_token_usage, 
	         status, fail_remark, create_time, update_time
		FROM
			sub_task_record AS t1
		<where>
        	1=1
	        <if test="subTaskId != null and subTaskId != ''">
	           	AND t1.sub_task_id = #{subTaskId}
            </if>
	        <if test="taskId != null and taskId != ''">
	           	AND t1.task_id = #{taskId}
            </if>
	        <if test="startTime != null and startTime != ''">
	           	AND t1.start_time = #{startTime}
            </if>
	        <if test="endTime != null and endTime != ''">
	           	AND t1.end_time = #{endTime}
            </if>
	        <if test="finishTime != null and finishTime != ''">
	           	AND t1.finish_time = #{finishTime}
            </if>
	        <if test="url != null and url != ''">
	           	AND t1.url = #{url}
            </if>
	        <if test="status != null and status != ''">
	           	AND t1.status = #{status}
            </if>
	        <if test="failRemark != null and failRemark != ''">
	           	AND t1.fail_remark = #{failRemark}
            </if>
        </where>
    </select>

</mapper>
