package com.my.college.service.impl;

import com.my.college.mybatis.entity.Position;
import com.my.college.mybatis.mapper.PositionMapper;
import com.my.college.service.PositionService;
import com.my.college.exception.BusinessException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.college.controller.dto.position.PositionInsertDTO;
import com.my.college.controller.dto.position.PositionPageDTO;
import com.my.college.controller.dto.position.PositionDeleteDTO;
import com.my.college.controller.vo.position.PositionDetailVO;
import com.my.college.controller.vo.position.PositionPageVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 职位表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class PositionServiceImpl extends ServiceImpl<PositionMapper, Position> implements PositionService {


	@Transactional
	@Override
	public void insert(PositionInsertDTO insertDTO) {
		// 参数校验
		if (insertDTO == null || StrUtil.isBlank(insertDTO.getPosition())) {
			BusinessException.by("职位名称不能为空");
		}

		// 检查职位是否已存在
		Position existingPosition = this.baseMapper.selectById(insertDTO.getPosition());
		if (existingPosition != null) {
			BusinessException.by("职位 [{}] 已存在", insertDTO.getPosition());
		}

		// 创建新职位
		Position entity = BeanUtil.copyProperties(insertDTO, Position.class);
		this.save(entity);
	}

	@Override
	public PositionDetailVO detail(String id) {
		Position entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, PositionDetailVO.class);
	}

	@Override
	public Page<PositionPageVO> page(PositionPageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Override
	public List<Position> list() {
		return super.list();
	}

	@Transactional
	@Override
	public void delete(PositionDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}
}
