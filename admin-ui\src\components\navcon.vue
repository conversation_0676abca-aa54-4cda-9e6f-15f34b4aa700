/**
* 头部菜单
*/
<template>
  <el-menu class="el-menu-demo" mode="horizontal" background-color="#334157" text-color="#fff" active-text-color="#fff">
    <el-button class="buttonimg">
      <img class="showimg" :src="collapsed?imgsq:imgshow" @click="toggle(collapsed)">
    </el-button>
    <el-submenu index="2" class="submenu">
      <template slot="title"><i class="el-icon-s-custom"></i> {{userEntity.username}} </template>
      <el-menu-item @click="popWin('MyPasswordEdit')" index="2-3">修改密码</el-menu-item>
      <el-menu-item @click="exit()" index="2-5">退出登录</el-menu-item>
    </el-submenu>

    <!-- Add/Edit -->
    <MyPasswordEdit ref="MyPasswordEdit"></MyPasswordEdit>
  </el-menu>
</template>
<script>
import MyPasswordEdit from '../views/My/MyPasswordEdit'

export default {
  name: 'navcon',
  data() {
    return {
      collapsed: true,
      imgshow: require('../assets/img/show.png'),
      imgsq: require('../assets/img/sq.png'),
      userEntity : undefined,
      loading: false,
    }
  },
  components: {
    MyPasswordEdit,
  },
  created() {
    this.userEntity = JSON.parse(localStorage.getItem('userEntity'))
  },
  methods: {
    // 弹窗
    popWin(windownName) {
      if (this.$refs[windownName]){
        this.$refs[windownName].show();
      }
    },
    // 退出登录
    exit() {
      this.$confirm('是否退出登录 ?', '退出登录', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          setTimeout(() => {
            this.$store.commit('logout', 'false')
            this.$router.push({ path: '/login' })
            this.$message({ type: 'success', message: 'Success!'})
          }, 1000)
        })
        .catch(() => {
          this.$message({ type: 'info', message: '已取消'})
        })
    },
    // 切换显示
    toggle(showtype) {
      this.collapsed = !showtype
      this.$root.Bus.$emit('toggle', this.collapsed)
    },
  }
}
</script>
<style scoped>
.el-menu-vertical-demo:not(.el-menu--collapse) {
  border: none;
}
.submenu {
  float: right;
}
.buttonimg {
  height: 60px;
  background-color: transparent;
  border: none;
}
.showimg {
  width: 26px;
  height: 26px;
  position: absolute;
  top: 17px;
  left: 17px;
}
.showimg:active {
  border: none;
}
</style>