<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.college.mybatis.mapper.TaskLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.college.mybatis.entity.TaskLog">
        <id column="log_id" property="logId" />
        <result column="task_id" property="taskId" />
        <result column="content" property="content" />
        <result column="type" property="type" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        log_id, task_id, content, type, create_time
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.college.controller.vo.task_log.TaskLogPageVO">
		SELECT
			log_id, task_id, content, type, create_time
		FROM
			task_log AS t1
		<where>
        	1=1
	        <if test="logId != null and logId != ''">
	           	AND t1.log_id = #{logId}
            </if>
	        <if test="taskId != null and taskId != ''">
	           	AND t1.task_id = #{taskId}
            </if>
	        <if test="content != null and content != ''">
	           	AND t1.content = #{content}
            </if>
            <if test="type != null and type != ''">
	           	AND t1.type = #{type}
            </if>
	        <if test="createTime != null and createTime != ''">
	           	AND t1.create_time = #{createTime}
            </if>
        </where>
    </select>

</mapper>
