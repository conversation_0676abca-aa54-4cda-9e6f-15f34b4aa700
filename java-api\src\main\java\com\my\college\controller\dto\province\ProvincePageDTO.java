package com.my.college.controller.dto.province;

import java.io.Serializable;

import com.alibaba.fastjson2.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.college.controller.vo.province.ProvincePageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_美国50个州州名
 *
 * <AUTHOR>
 * @date 2025-05-25
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class ProvincePageDTO 
						extends PageDTO<ProvincePageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字母缩写
     */
    private String provinceId;

    /**
     * 英文名称
     */
    private String englishName;

    /**
     * 中文名称
     */
    private String chineseName;

}
