package com.my.college.mybatis.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.college.controller.dto.sys_user.SysUserPageDTO;
import com.my.college.controller.vo.sys_user.SysUserPageVO;
import com.my.college.mybatis.entity.SysUser;

/**
 * system user Mapper 接口
 *
 * @date 2024-05-20
 */
public interface SysUserMapper extends BaseMapper<SysUser> {


	/**
	 * 查询用户
	 * @param username
	 * @param password
	 * @return
	 */
	default SysUser getByUsernameAndPassword(String username, String password) {
		LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<SysUser>()
				.eq(SysUser::getUsername, username)
	            .eq(SysUser::getPassword, password);
        return this.selectOne(wrapper);
	}

	/**
	 * 更新密码
	 * @param username
	 * @param password 
	 */
	default void updatePassword(String username, String password) {
		Wrapper<SysUser> wrapper = new LambdaUpdateWrapper<SysUser>()
				.set(SysUser::getPassword, password)
				.eq(SysUser::getUsername, username);
		SysUser entity = SysUser.builder().username(username).build();
		this.update(entity, wrapper);
	}
	
	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SysUserPageVO> page(SysUserPageDTO pageDTO);
	
	
}
