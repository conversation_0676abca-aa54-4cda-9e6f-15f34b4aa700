package com.my.college.controller;


import org.springframework.web.bind.annotation.RequestMapping;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import com.my.college.controller.dto.position.PositionInsertDTO;
import com.my.college.controller.dto.position.PositionPageDTO;
import com.my.college.controller.dto.position.PositionDeleteDTO;
import com.my.college.controller.vo.position.PositionDetailVO;
import com.my.college.controller.vo.position.PositionPageVO;
import com.my.college.controller.vo.StdResp;
import com.my.college.mybatis.entity.Position;

import java.util.List;

import com.my.college.service.PositionService;

import org.springframework.web.bind.annotation.RestController;

/**
 * 职位表 
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/api/position")
@RequiredArgsConstructor
public class PositionController {

    private final PositionService positionService;

    /**
    * 新增
    */
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody PositionInsertDTO insertDTO) {
    	this.positionService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<PositionDetailVO> detail(@PathVariable String id) {
    	return StdResp.success(this.positionService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<PositionPageVO>> page(PositionPageDTO pageDTO) {
        Page<PositionPageVO> page = this.positionService.page(pageDTO);
        return StdResp.success(page);
    }

    /**
     * 查询全部职位
     */
    @GetMapping(value = "list")
    public StdResp<List<Position>> list() {
    	List<Position> data = this.positionService.list();
    	return StdResp.success(data);
    }

    /**
    * 批量删除(物理删除)
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody PositionDeleteDTO deleteDTO) {
    	this.positionService.delete(deleteDTO);
		return StdResp.success();
    }
    
}
