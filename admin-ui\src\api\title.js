import { reqGet, reqPut, reqDelete, reqPost } from './axiosFun';

// 分页
export const titlePage = (params) => { return reqGet("/title/page", params) };

// 获取全部标题列表
export const titleList = (params) => { return reqGet("/title/list", params) };

// 获取详情
export const titleDetail = (params) => { return reqGet("/title/" + params.title) };

// 创建标题
export const createTitle = (params) => {return reqPost("/title", params);};

// 更新标题
export const updateTitle = (params) => {return reqPut("/title", params);};

// 删除标题
export const deleteTitle = (params) => { return reqDelete("/title", params) };
