package com.my.college.controller.dto.address_book;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_通讯录表
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class AddressBookUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
	@NotNull(message="id不能为空")
    private Integer id;

    /**
     * 学校ID
     */
	@NotNull(message="schoolId不能为空")
    private Integer schoolId;

    /**
     * 部门
     */
    private String department;

    /**
     * 联系人姓名
     */
	@NotBlank(message="contactName不能为空")
    private String contactName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 职位
     */
    private String position;

    /**
     * 电话号码
     */
    private String phone;

}
