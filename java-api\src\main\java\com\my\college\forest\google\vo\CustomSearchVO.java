package com.my.college.forest.google.vo;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson2.annotation.JSONField;
import com.my.college.forest.google.onerror.CustomSearchErrCode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor @AllArgsConstructor @Builder @Data 
public class CustomSearchVO {

    @JSONField(name = "items", ordinal = 1)
    @Builder.Default
    List<Items> items = new ArrayList<Items>();

    @JSONField(name = "queries", ordinal = 2)
    @Builder.Default
    Queries queries = new Queries();
    
    @JSONField(name = "error", ordinal = 3)
    Error error;

    
    /**
     * 是否成功
     * @return
     */
    public boolean success() {
    	return this.error == null;
    }
    
    /**
     * 返回异常信息
     * @return
     */
    public String getErrorMessage() {
    	if (!this.success()) {
    		return this.error.getMessage();
    	} else {
    		return null;
    	}
    }
    
    /**
     * 返回异常信息(中文)
     * @return
     */
    public String getErrorMessageChinese() {
    	String errMsg = this.getErrorMessage();
    	if (errMsg == null) {
    		return null;
    	}
    	// 20525.06.22 Quota exceeded for quota metric 'Queries' and limit 'Queries per day' of service 'customsearch.googleapis.com' for consumer 'project_number:75561874528'.
    	if (errMsg.startsWith("Quota exceeded for quota metric 'Queries' and limit 'Queries per day' of service 'customsearch.googleapis.com'")) {
    		errMsg =  "免费账号达到每日接口限额";
    	}
    	switch (errMsg) {
			case "Request contains an invalid argument.":
				errMsg = CustomSearchErrCode.E011.getLabel();
				break;
				
			case "API key not valid. Please pass a valid API key.":
				errMsg = CustomSearchErrCode.E012.getLabel();
				break;
				
			case "免费账号达到每日接口限额":
				errMsg = CustomSearchErrCode.E013.getLabel();
				break;
				
			case "Request throttled due to daily limit being reached.":
				errMsg = CustomSearchErrCode.E014.getLabel();
				break;
		}
    	return errMsg;
    }
    

    // ======== 以下为内部类 =========
    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
    public static class Request {

        @JSONField(name = "count", ordinal = 1)
        String count;

        @JSONField(name = "cx", ordinal = 2)
        String cx;

        @JSONField(name = "inputEncoding", ordinal = 3)
        String inputEncoding;

        @JSONField(name = "outputEncoding", ordinal = 4)
        String outputEncoding;

        @JSONField(name = "safe", ordinal = 5)
        String safe;

        @JSONField(name = "searchTerms", ordinal = 6)
        String searchTerms;

        @JSONField(name = "startIndex", ordinal = 7)
        String startIndex;

        @JSONField(name = "title", ordinal = 8)
        String title;

        @JSONField(name = "totalResults", ordinal = 9)
        String totalResults;
    }

    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
    public static class NextPage {

        @JSONField(name = "count", ordinal = 1)
        String count;

        @JSONField(name = "cx", ordinal = 2)
        String cx;

        @JSONField(name = "inputEncoding", ordinal = 3)
        String inputEncoding;

        @JSONField(name = "outputEncoding", ordinal = 4)
        String outputEncoding;

        @JSONField(name = "safe", ordinal = 5)
        String safe;

        @JSONField(name = "searchTerms", ordinal = 6)
        String searchTerms;

        @JSONField(name = "startIndex", ordinal = 7)
        String startIndex;

        @JSONField(name = "title", ordinal = 8)
        String title;

        @JSONField(name = "totalResults", ordinal = 9)
        String totalResults;
    }

    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
    public static class Queries {

        @JSONField(name = "nextPage", ordinal = 1)
        @Builder.Default
        List<NextPage> nextPage = new ArrayList<NextPage>();

        @JSONField(name = "request", ordinal = 2)
        @Builder.Default
        List<Request> request = new ArrayList<Request>();
    }

    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
    public static class Items {

//        @JSONField(name = "htmlTitle", ordinal = 1)
//        String htmlTitle;
//
//        @JSONField(name = "kind", ordinal = 2)
//        String kind;

        @JSONField(name = "link", ordinal = 3)
        String link;

//        @JSONField(name = "title", ordinal = 4)
//        String title;
    }

    @Data
    public static class Error {
    	
    	@JSONField(name = "code", ordinal = 1)
    	Integer code;
    	
    	@JSONField(name = "message", ordinal = 2)
    	String message;
    	
    }
    
}