package com.my.college.forest.google.onerror;

import com.alibaba.fastjson2.JSON;
import com.dtflys.forest.callback.OnError;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.my.college.forest.google.vo.CustomSearchVO;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 搜索的异常处理
 */
@Slf4j
public class CustomSearchOnError implements OnError {

	@Getter
	private String errMsg;
	

	@SuppressWarnings("rawtypes")
	@Override
	public void onError(ForestRuntimeException e, ForestRequest req, ForestResponse response) {
		if (response.getStatusCode() == -1) {
			this.errMsg = CustomSearchErrCode.E000.getLabel();
		} else {
			CustomSearchVO vo = JSON.parseObject(response.getContent(), CustomSearchVO.class);
			if (vo != null) {
				this.errMsg = vo.getErrorMessageChinese();
			} else {
				this.errMsg = CustomSearchErrCode.E020.getLabel();
			}
		}
		
		log.error("CustomSearchErrCode：{}, detail:{}", errMsg, e.getMessage());
	}
	
	/**
	 * 是否报错
	 * @return
	 */
	public boolean isError() {
		return StrUtil.isNotBlank(errMsg);
	}

}
