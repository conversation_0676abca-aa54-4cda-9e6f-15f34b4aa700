package com.my.college.forest.markdown.onerror;

import com.dtflys.forest.callback.OnError;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * Markdown接口异常处理
 */
@Slf4j
public class MarkdownOnError implements OnError {

	@Getter
	private String errMsg;
	

	@SuppressWarnings("rawtypes")
	@Override
	public void onError(ForestRuntimeException e, ForestRequest req, ForestResponse response) {
		this.errMsg = e.getMessage();
		log.error("Markdown接口异常：{}", errMsg);
	}
	
	/**
	 * 是否报错
	 * @return
	 */
	public boolean isError() {
		return StrUtil.isNotBlank(errMsg);
	}

}
