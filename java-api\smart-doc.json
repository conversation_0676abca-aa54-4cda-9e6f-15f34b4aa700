{"pathPrefix": "", "openUrl": "http://192.168.131.132:7700/api", "appToken": "f8853d50524142a3993447811e16041a", "debugEnvUrl": "http://lwc.npc-local.fxxf.online", "packageFilters": "", "allInOne": true, "isStrict": false, "coverOld": true, "tornaDebug": false, "inlineEnum": true, "debugEnvName": "local", "outPath": "target/doc", "requestHeaders": [{"name": "token", "type": "string", "desc": "请求头token", "required": false, "value": ""}], "dataDictionaries": [], "errorCodeDictionaries": []}