package com.my.college.enums.annotation;

import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 字符串作为key的枚举
 *
 * <AUTHOR>
 * @date 2019年7月29日
 */
@Slf4j
public class StringEnumValidator implements ConstraintValidator<StringEnumCheck, String> {

    private Class<?> enumClass;

    @Override
    public void initialize(StringEnumCheck constraintAnnotation) {
        this.enumClass = constraintAnnotation.enumClass();
    }

    /**
     * 检测enumClass
     *
     * @return
     */
    private boolean checkEnumClass(Type[] types) {
        for (Type type : types) {
            if (type.getTypeName().equals(StringEnum.class.getName())) {
                return true;
            }
        }
        throw new RuntimeException("枚举配置有误. enumClass:" + this.enumClass.getSimpleName() + "不是StringEnum");
    }
    
    @Override
    public boolean isValid(String val, ConstraintValidatorContext context) {
        Type[] types = enumClass.getGenericInterfaces();
        this.checkEnumClass(types);
        
        if (StringUtils.isBlank(val)) {
        	return true;
        }
        try {
            Method method = this.enumClass.getMethod("values");
            StringEnum[] enums = (StringEnum[]) method.invoke(null);
            for (StringEnum en : enums) {
                if (en.name().equals(val)) {
                    return true;
                }
            }
            List<String> keys = Arrays.asList(enums).stream().map(e -> e.name()).collect(Collectors.toList());
            log.error("枚举【{}】的取值范围是:{}", this.enumClass.getSimpleName(), keys);
        } catch (Exception e) {
        	//ignore 
        }
        return false;
    }

}
