package com.my.college.task.subtask;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.my.college.mybatis.entity.SubTaskRecord;
import com.my.college.service.SubTaskRecordService;

import lombok.extern.slf4j.Slf4j;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
class AIProcessTest {
	
	@Autowired
	AIProcess aiProcess;
	
	@Autowired
	SubTaskRecordService subTaskRecordService;
	

	@Test
	void testProcess() {
		String subTaskId = "1937287745735516160";
		SubTaskRecord subTaskRecord = this.subTaskRecordService.getById(subTaskId);
		String taskId = subTaskRecord.getTaskId();
		String url = subTaskRecord.getUrl();
		String markdown = subTaskRecord.getMarkdownResponse();
		this.aiProcess.process(taskId, subTaskId, url, markdown);
		while (true) {
			
		}
	}

}
