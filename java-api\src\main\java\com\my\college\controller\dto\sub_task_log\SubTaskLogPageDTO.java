package com.my.college.controller.dto.sub_task_log;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.alibaba.fastjson2.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.college.controller.vo.sub_task_log.SubTaskLogPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_子任务日志表
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SubTaskLogPageDTO 
						extends PageDTO<SubTaskLogPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增日志ID
     */
    private Integer logId;

    /**
     * 关联的任务编号
     */
    private String subTaskId;

    /**
     * 日志类型(ERROR/WARNING/SUCCESS/INFO)
     */
    private String type;

    /**
     * 日志详细内容
     */
    private String content;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
