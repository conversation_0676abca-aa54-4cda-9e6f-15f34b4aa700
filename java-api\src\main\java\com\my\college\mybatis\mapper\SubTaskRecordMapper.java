package com.my.college.mybatis.mapper;

import com.my.college.controller.dto.sub_task_record.SubTaskRecordPageDTO;
import com.my.college.controller.vo.sub_task_record.SubTaskRecordPageVO;
import com.my.college.mybatis.entity.SubTaskRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 子任务记录表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-06-22
 */
public interface SubTaskRecordMapper extends BaseMapper<SubTaskRecord> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SubTaskRecordPageVO> page(SubTaskRecordPageDTO pageDTO);
	
}
