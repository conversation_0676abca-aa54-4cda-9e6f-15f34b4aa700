package com.my.college.service.check.impl;

import java.lang.reflect.Field;

import org.apache.commons.lang3.StringUtils;

import com.my.college.controller.vo.sys_param.SysParamVO;
import com.my.college.enums.annotation.StringEnum;
import com.my.college.forest.google.GoogleClient;
import com.my.college.forest.google.dto.CustomSearchDTO;
import com.my.college.forest.google.onerror.CustomSearchErrCode;
import com.my.college.forest.google.onerror.CustomSearchOnError;
import com.my.college.service.SysParamService;
import com.my.college.service.check.AbstractCheck;
import com.my.college.service.check.result.CheckResult;

import cn.hutool.extra.spring.SpringUtil;

/**
 * 搜索引擎ID
 * <AUTHOR>
 */
public class GoogleEngineIDCheck extends AbstractCheck {
	
    private static final String KEY_WORD_TEST = "hello world";
    
	
	@Override
    protected CheckResult doCheck(Object fieldValue, Field field, Object obj) {
        String val = (String) fieldValue;
        if (StringUtils.isBlank(val)) {
            return fail(this.fieldLabel + "不能为空");
        }
        
		SysParamVO sysParam = SpringUtil.getBean(SysParamService.class).get();
		String key = sysParam.getGoogleApiKey();
		String cx = sysParam.getGoogleEngineID();
		CustomSearchDTO customSearchDTO = new CustomSearchDTO(key, cx, KEY_WORD_TEST);
		
		CustomSearchOnError onError = new CustomSearchOnError();
		SpringUtil.getBean(GoogleClient.class).customSearch(customSearchDTO, onError);
		if (onError.isError()) {
			String errMsg = onError.getErrMsg();
			CustomSearchErrCode errCode = StringEnum.labelOf(CustomSearchErrCode.class, errMsg);
			if (errCode != CustomSearchErrCode.E012) {
				return fail(errMsg);
			}
		} 
		
        return success();
    }
}
