package com.my.college.service.check;

import java.lang.reflect.Field;
import java.util.List;

import org.junit.jupiter.api.Test;

import com.my.college.controller.vo.sys_param.SysParamCheckVO;
import com.my.college.controller.vo.sys_param.SysParamVO;
import com.my.college.service.check.impl.AiApiKeyCheck;
import com.my.college.service.check.impl.AiModelCheck;
import com.my.college.service.check.result.CheckResult;

import lombok.extern.slf4j.Slf4j;

/**
 * Check体系测试类
 * 
 * <AUTHOR>
 */
@Slf4j
public class CheckTest {

    @Test
    public void testAiModelCheck() throws Exception {
        // 获取字段
        Field aiModelField = SysParamVO.class.getDeclaredField("aiModel");
        
        // 创建检测器
        AiModelCheck checker = new AiModelCheck();
        
        // 测试空值
        CheckResult result1 = checker.check(null, aiModelField, null);
        log.info("空值测试: {}", result1);
        
        // 测试无效值
        CheckResult result2 = checker.check("invalid-model", aiModelField, null);
        log.info("无效值测试: {}", result2);
        
        // 测试有效值
        CheckResult result3 = checker.check("deepseek-chat", aiModelField, null);
        log.info("有效值测试: {}", result3);
    }
    
    @Test
    public void testApiKeyCheck() throws Exception {
        // 获取字段
        Field apiKeyField = SysParamVO.class.getDeclaredField("aiApiKey");
        
        // 创建检测器
        AiApiKeyCheck checker = new AiApiKeyCheck();
        
        // 测试空值
        CheckResult result1 = checker.check("", apiKeyField, null);
        log.info("空值测试: {}", result1);
        
        // 测试短密钥
        CheckResult result2 = checker.check("short-key", apiKeyField, null);
        log.info("短密钥测试: {}", result2);
        
        // 测试有效密钥
        CheckResult result3 = checker.check("sk-1234567890abcdef1234567890abcdef", apiKeyField, null);
        log.info("有效密钥测试: {}", result3);
    }
    
    @Test
    public void testSysParamVOCheck() {
        // 创建测试对象
        SysParamVO sysParam = SysParamVO.builder()
            .aiModel("deepseek-chat")
            .aiApiKey("sk-1234567890abcdef1234567890abcdef")
            .build();
        
        // 执行检测
        SysParamCheckVO checkResult = sysParam.check();
        
        log.info("系统参数检测结果: {}", checkResult);
        
        // 检查无效项
        List<CheckResult> invalidItems = checkResult.invalidItems();
        log.info("无效项数量: {}", invalidItems.size());
        
        for (CheckResult item : invalidItems) {
            log.info("无效项: {} - {}", item.getLabel(), item.getMessage());
        }
    }
} 