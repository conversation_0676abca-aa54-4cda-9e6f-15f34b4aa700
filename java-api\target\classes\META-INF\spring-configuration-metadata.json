{"groups": [{"name": "com.my.college.forest.google.address", "type": "com.my.college.forest.google.address.GoogleAddress", "sourceType": "com.my.college.forest.google.address.GoogleAddress"}, {"name": "com.my.college.forest.markdown.address", "type": "com.my.college.forest.markdown.address.MarkdownAddress", "sourceType": "com.my.college.forest.markdown.address.MarkdownAddress"}, {"name": "com.my.college.forest.siliconflow.address", "type": "com.my.college.forest.siliconflow.address.SiliconflowAddress", "sourceType": "com.my.college.forest.siliconflow.address.SiliconflowAddress"}], "properties": [{"name": "com.my.college.forest.google.address.host", "type": "java.lang.String", "description": "IP", "sourceType": "com.my.college.forest.google.address.GoogleAddress"}, {"name": "com.my.college.forest.google.address.port", "type": "java.lang.Integer", "description": "端口", "sourceType": "com.my.college.forest.google.address.GoogleAddress"}, {"name": "com.my.college.forest.google.address.scheme", "type": "java.lang.String", "description": "协议(http或https)", "sourceType": "com.my.college.forest.google.address.GoogleAddress"}, {"name": "com.my.college.forest.markdown.address.host", "type": "java.lang.String", "description": "IP", "sourceType": "com.my.college.forest.markdown.address.MarkdownAddress"}, {"name": "com.my.college.forest.markdown.address.port", "type": "java.lang.Integer", "description": "端口", "sourceType": "com.my.college.forest.markdown.address.MarkdownAddress"}, {"name": "com.my.college.forest.markdown.address.scheme", "type": "java.lang.String", "description": "协议(http或https)", "sourceType": "com.my.college.forest.markdown.address.MarkdownAddress"}, {"name": "com.my.college.forest.siliconflow.address.host", "type": "java.lang.String", "description": "IP", "sourceType": "com.my.college.forest.siliconflow.address.SiliconflowAddress"}, {"name": "com.my.college.forest.siliconflow.address.port", "type": "java.lang.Integer", "description": "端口", "sourceType": "com.my.college.forest.siliconflow.address.SiliconflowAddress"}, {"name": "com.my.college.forest.siliconflow.address.scheme", "type": "java.lang.String", "description": "协议(http或https)", "sourceType": "com.my.college.forest.siliconflow.address.SiliconflowAddress"}], "hints": []}