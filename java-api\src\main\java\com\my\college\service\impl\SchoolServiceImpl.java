package com.my.college.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.college.controller.dto.school.SchoolDeleteDTO;
import com.my.college.controller.dto.school.SchoolInsertDTO;
import com.my.college.controller.dto.school.SchoolPageDTO;
import com.my.college.controller.dto.school.SchoolUpdateDTO;
import com.my.college.controller.dto.school.SchoolUploadDTO;
import com.my.college.controller.vo.school.SchoolDetailVO;
import com.my.college.controller.vo.school.SchoolPageVO;
import com.my.college.mybatis.entity.Province;
import com.my.college.mybatis.entity.School;
import com.my.college.mybatis.mapper.SchoolMapper;
import com.my.college.service.AddressBookService;
import com.my.college.service.ProvinceService;
import com.my.college.service.SchoolService;
import com.my.college.util.ColumnLambda;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 学校表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SchoolServiceImpl extends ServiceImpl<SchoolMapper, School> implements SchoolService {

	private final AddressBookService addressBookService;
	private final ProvinceService provinceService;
	

	@Transactional
	@Override
	public void insert(SchoolInsertDTO insertDTO) {
		School entity = BeanUtil.copyProperties(insertDTO, School.class);
		entity.setUpdateTime(LocalDateTime.now());
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(SchoolUpdateDTO updateDTO) {
		School entity = BeanUtil.copyProperties(updateDTO, School.class);
		entity.setUpdateTime(LocalDateTime.now());
		this.baseMapper.updateById(entity);
	}

	@Override
	public SchoolDetailVO detail(Integer id) {
		School entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, SchoolDetailVO.class);
	}

	@Override
	public Page<SchoolPageVO> page(SchoolPageDTO pageDTO) {
		List<OrderItem> orders = pageDTO.getOrders();
		if (CollectionUtil.isEmpty(orders)) {
			orders.add(OrderItem.desc(new ColumnLambda<School>().columnsToString(School::getSchoolId)));
		}
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(SchoolDeleteDTO deleteDTO) {
		List<Integer> schoolIdList = deleteDTO.getIdList();
		// 同时删除通讯录表中的
		this.addressBookService.deleteBySchoolId(schoolIdList);
		this.baseMapper.deleteBatchIds(schoolIdList);
	}

	@Transactional
	@Override
	public void batchInsert(Set<SchoolUploadDTO> validList) {
		if (CollectionUtil.isEmpty(validList)) {
			return;
		}
		
		int insertCount = 0;
		int updateCount = 0;
		
		for (SchoolUploadDTO uploadDTO : validList) {
			// 根据中文名称查询是否存在
			School existingSchool = this.getByChineseName(uploadDTO.getChineseName());
			
			// 处理省份匹配逻辑
			String validProvinceId = null;
			if (StrUtil.isNotBlank(uploadDTO.getProvinceId())) {
				Province province = provinceService.findByIdOrName(uploadDTO.getProvinceId());
				if (province != null) {
					validProvinceId = province.getProvinceId(); // 使用省份缩写
					log.debug("省份匹配成功：{} -> {}", uploadDTO.getProvinceId(), validProvinceId);
				} else {
					log.warn("省份匹配失败，忽略provinceId值：{}", uploadDTO.getProvinceId());
				}
			}
			
			if (existingSchool != null) {
				// 学校存在，更新学校信息
				existingSchool.setEnglishName(uploadDTO.getEnglishName());
				existingSchool.setWebsite(uploadDTO.getWebsite());
				existingSchool.setRemark(uploadDTO.getRemark());
				existingSchool.setProvinceId(validProvinceId); // 设置匹配成功的省份缩写或null
				existingSchool.setUpdateTime(LocalDateTime.now());
				this.updateById(existingSchool);
				updateCount++;
			} else {
				// 学校不存在，新增学校
				School newSchool = BeanUtil.copyProperties(uploadDTO, School.class);
				newSchool.setProvinceId(validProvinceId); // 设置匹配成功的省份缩写或null
				newSchool.setUpdateTime(LocalDateTime.now());
				this.save(newSchool);
				insertCount++;
			}
		}
		
		log.info("批量处理学校数据成功，新增{}条，更新{}条", insertCount, updateCount);
	}

	@Override
	public School getByChineseName(String chineseName) {
		if (StrUtil.isBlank(chineseName)) {
			return null;
		}
		
		LambdaQueryWrapper<School> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(School::getChineseName, chineseName);
		return this.getOne(queryWrapper);
	}
}
