package com.my.college.util;

import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validator;
import javax.validation.groups.Default;

import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.hutool.extra.spring.SpringUtil;

/**
 *   
 * JSR303 Validator(Hibernate Validator)工具类 <p>
 *
 * @date 2016年9月5日 下午3:00:02 
 * @see 详情见： http://code.taobao.org/p/KIT/diff/4/JeeSite/src/main/java/com/thinkgem/jeesite/common/beanvalidator
 * JSR303是JavaEE6中的一项子规范,叫做BeanValidation,它的官方参考实现是hibernate-validator 	<br/>
 * BeanValidation现在一共有两个规范:BeanValidation1.0(即JSR303)和BeanValidation1.1(即JSR349) <br/>
 * BeanValidation的官网是http://beanvalidation.org/ 										<br/>
 * 关于JSR303的详细说明,请参考http://jcp.org/en/jsr/detail?id=303 							<br/>
 * 关于JSR349的详细说明,请参考http://jcp.org/en/jsr/detail?id=349 							<br/>
 * =========================================																<br/>
 * JSR303规范主要用于对JavaBean中的字段的值的验证,使得验证逻辑从业务代码中脱离出来 								<br/>
 * JSR303定义了基于注解方式的JavaBean验证元数据模型和API,也可通过XML进行元数据定义,但注解会覆盖XML的定义 		<br/>
 * JSR303主要是对JavaBean进行验证,而没有指定方法级别(参数or返回值)、依赖注入等验证,因此催生了JSR349规范 			<br/>
 * JSR349规范目前处于草案状态,它主要支持依赖注入的验证和方法级别的验证(方法的参数和返回值) 						<br/>
 * Spring3.1目前已经完全支持依赖注入验证和方法级别的验证了,只不过不是原生的(JSR349规范还是草案嘛) 				<br/>
 * 关于这个的详细说明,可以参考此爷的文章http://www.iteye.com/topic/1123007
 */
@SuppressWarnings({"unchecked", "rawtypes"})
public class BeanValidators {

    /**
     * 调用JSR303的validate方法, 逐一验证对象的指定字段
     *
     * 2016.11.03
     */
    public static void validateWithException(Object object, String... fieldNames)
            throws ConstraintViolationException {
        Validator validator = SpringUtil.getBean(Validator.class);
        if (fieldNames != null) {
            Set constraintViolations = new LinkedHashSet();
            for (String fieldName : fieldNames) {
                constraintViolations.addAll(validator.validateProperty(object, fieldName)); // 堆积各字段的违规信息
            }

            if (!constraintViolations.isEmpty()) {
                List<String> msgs = extractMessage(constraintViolations);
                throw new ConstraintViolationException(msgs.toString(), constraintViolations);   // 所有字段的违规信息
            }
        }
    }

    /**
     * 调用JSR303的validate方法, 验证失败时抛出ConstraintViolationException.
     */
    public static void validateWithException(Object object, Class<?>... groups)
            throws ConstraintViolationException {
        Set constraintViolations = BeanValidators.validate(object, groups);
        if (!constraintViolations.isEmpty()) {
            throw new ConstraintViolationException(constraintViolations);
        }
    }


    /**
     * 调用JSR303的validate方法, 验证失败时抛出ConstraintViolationException.
     */
    public static void validateWithException(Object object)
            throws ConstraintViolationException {
        Set constraintViolations = BeanValidators.validate(object, Default.class);
        if (!constraintViolations.isEmpty()) {
            throw new ConstraintViolationException(constraintViolations);
        }
    }

    /**
     *   
     * 调用JSR303的validate方法
     *
     * @return Set<ConstraintViolation   <   T>> 返回违规信息
     */
    private static <T> Set<ConstraintViolation<T>> validate(T t, Class<?>... groups) {
        Validator validator = SpringUtil.getBean(Validator.class);
        Set<ConstraintViolation<T>> set = validator.validate(t, groups);
        return set;
    }

    /**
     * 抽取违规信息数组
     * 即：转换ConstraintViolationException中的Set<ConstraintViolations>中为List<message>.
     */
    public static List<String> extractMessage(ConstraintViolationException e) {
        return extractMessage(e.getConstraintViolations());
    }

    /**
     * 抽取违规信息数组
     * 即：转换ConstraintViolationException中的Set<ConstraintViolations>中为List<message>.
     */
    public static List<String> extractMessage(BindingResult bdResult) {
        List<String> errMsgList = new LinkedList<String>();
        if (bdResult.hasErrors()) {
            for (ObjectError err : bdResult.getAllErrors()) {
                errMsgList.add(err.getDefaultMessage()); // 违规信息
            }
        }
        return errMsgList;
    }

    /**
     * 抽取违规信息数组
     * 即：转换Set<ConstraintViolation>为List<message>
     */
    public static List<String> extractMessage(Set<? extends ConstraintViolation> constraintViolations) {
        List<String> errorMessages = Lists.newArrayList();
        for (ConstraintViolation violation : constraintViolations) {
            errorMessages.add(violation.getMessage());
        }
        return errorMessages;
    }

    /**
     * 辅助方法, 转换ConstraintViolationException中的Set<ConstraintViolations>为Map<property, message>.
     */
    public static Map<String, String> extractPropertyAndMessage(ConstraintViolationException e) {
        return extractPropertyAndMessage(e.getConstraintViolations());
    }

    /**
     * 辅助方法, 转换Set<ConstraintViolation>为Map<property, message>.
     */
    public static Map<String, String> extractPropertyAndMessage(Set<? extends ConstraintViolation> constraintViolations) {
        Map<String, String> errorMessages = Maps.newHashMap();
        for (ConstraintViolation violation : constraintViolations) {
            errorMessages.put(violation.getPropertyPath().toString(), violation.getMessage());
        }
        return errorMessages;
    }

    /**
     * 辅助方法, 转换ConstraintViolationException中的Set<ConstraintViolations>为List<propertyPath message>.
     */
    public static List<String> extractPropertyAndMessageAsList(ConstraintViolationException e) {
        return extractPropertyAndMessageAsList(e.getConstraintViolations(), null);
    }

    /**
     * 辅助方法, 转换Set<ConstraintViolations>为List<propertyPath message>.
     */
    public static List<String> extractPropertyAndMessageAsList(Set<? extends ConstraintViolation> constraintViolations) {
        return extractPropertyAndMessageAsList(constraintViolations, null);
    }

    /**
     * 辅助方法, 转换ConstraintViolationException中的Set<ConstraintViolations>为List<propertyPath +separator+ message>.
     */
    public static List<String> extractPropertyAndMessageAsList(ConstraintViolationException e, String separator) {
        return extractPropertyAndMessageAsList(e.getConstraintViolations(), separator);
    }

    /**
     * 辅助方法, 转换Set<ConstraintViolation>为List<propertyPath +separator+ message>.
     *
     * @date 2017.11.23
     */
    public static List<String> extractPropertyAndMessageAsList(Set<? extends ConstraintViolation> constraintViolations, String separator) {
        List<String> errorMessages = Lists.newArrayList();
        for (ConstraintViolation violation : constraintViolations) {
            // String path = violation.getPropertyPath().toString();
            if (separator == null) {
            	errorMessages.add(violation.getMessage());
            } else {
            	/* 字段+异常信息
            	errorMessages.add(path + separator + violation.getMessage());
            	*/
            	// 仅返回异常信息
            	errorMessages.add(violation.getMessage());
            }
        }
        return errorMessages;
    }
    
}