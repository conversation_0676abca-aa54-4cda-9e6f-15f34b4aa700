package com.my.college.forest;

import static org.junit.jupiter.api.Assertions.fail;

import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.common.collect.Lists;
import com.my.college.controller.vo.sys_param.SysParamVO;
import com.my.college.forest.deepseek.DeepSeekClient;
import com.my.college.forest.deepseek.dto.format.DeepSeekResponseFormat;
import com.my.college.forest.deepseek.dto.format.TextFormat;
import com.my.college.forest.deepseek.dto.message.DeepSeekMessage;
import com.my.college.forest.deepseek.dto.message.SystemMessage;
import com.my.college.forest.deepseek.dto.message.UserMessage;
import com.my.college.forest.deepseek.enums.DeepSeekModel;
import com.my.college.forest.deepseek.enums.MaxTokens;
import com.my.college.service.SysParamService;
import com.my.college.util.OfficeUtil;

import lombok.extern.slf4j.Slf4j;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
class DeepSeekClientTest {
	
	String folder = "C:\\Users\\<USER>\\Desktop\\test\\";

	@Autowired
	DeepSeekClient deepSeekClient;
	
	@Autowired
	SysParamService sysParamService;

	@Test
	void testChatCompletionsOnePlusOne() {
		String model = DeepSeekModel.reasoner.getCode();
		List<DeepSeekMessage> messages = Lists.newLinkedList();
		messages.add(new SystemMessage("你是数学专家"));
		messages.add(new UserMessage("1+1=?"));
		Integer maxTokens = MaxTokens._8K.getVal();
		Double temperature = 1.0;
		DeepSeekResponseFormat responseFormat = new TextFormat();
		
		SysParamVO sysParam = this.sysParamService.get();
		String apiKey = sysParam.getAiApiKey();
		this.deepSeekClient.chatCompletions(model, messages, maxTokens, temperature, responseFormat, apiKey);
		while(true) {
			
		}
	}
	
	@Test
	void testChatCompletionsByOfficeContent() {
		String model = DeepSeekModel.reasoner.getCode();
		List<DeepSeekMessage> messages = Lists.newLinkedList();
		Integer maxTokens = MaxTokens._8K.getVal();
		Double temperature = 1.0;
		DeepSeekResponseFormat responseFormat = new TextFormat();
		
		SysParamVO sysParam = this.sysParamService.get();
		String apiKey = sysParam.getAiApiKey();
		
		String statusContent = OfficeUtil.excel2Csv(folder + "status.xlsx");
		String strategyContent = OfficeUtil.word2Markdown(folder + "strategy.docx");
		String cmdTplContent = OfficeUtil.excel2Csv(folder + "cmd_tpl.xlsx");
		
		messages.add(new SystemMessage(sysParam.getAiSystemPrompt()));
		messages.add(new SystemMessage("status.xlsx内容如下:\n" + statusContent));
		messages.add(new SystemMessage("strategy.docx内容如下:\n" + strategyContent));
		messages.add(new SystemMessage("cmd_tpl.xlsx内容如下:\n" + cmdTplContent));
		messages.add(new UserMessage(sysParam.getAiUserPrompt()));
		
		this.deepSeekClient.chatCompletions(model, messages, maxTokens, temperature, responseFormat, apiKey);
		
		while(true) {
			
		}
	}
	
	@Test
	void testChatCompletionsByUrl() {
		String model = DeepSeekModel.reasoner.getCode();
		List<DeepSeekMessage> messages = Lists.newLinkedList();
		Integer maxTokens = MaxTokens._8K.getVal();
		Double temperature = 1.0;
		DeepSeekResponseFormat responseFormat = new TextFormat();
		
		SysParamVO sysParam = this.sysParamService.get();
		String apiKey = sysParam.getAiApiKey();
		messages.add(new SystemMessage(sysParam.getAiSystemPrompt()));
		messages.add(new SystemMessage("http://fxxf.3vcn.work/ai-cmd-files/status.xlsx"));
		messages.add(new SystemMessage("http://fxxf.3vcn.work/ai-cmd-files/strategy.docx"));
		messages.add(new SystemMessage("http://fxxf.3vcn.work/ai-cmd-files/cmd_tpl.xlsx"));
		messages.add(new UserMessage(sysParam.getAiUserPrompt()));
		
		this.deepSeekClient.chatCompletions(model, messages, maxTokens, temperature, responseFormat, apiKey);
		
		while(true) {
			
		}
	}
	
	@Test
	void testChatCompletionsByBase64() {
		String model = DeepSeekModel.reasoner.getCode();
		List<DeepSeekMessage> messages = Lists.newLinkedList();
		Integer maxTokens = MaxTokens._8K.getVal();
		Double temperature = 1.0;
		DeepSeekResponseFormat responseFormat = new TextFormat();
		
		SysParamVO sysParam = this.sysParamService.get();
        
		
		String apiKey = sysParam.getAiApiKey();
		messages.add(new SystemMessage(sysParam.getAiSystemPrompt()));
		messages.add(new UserMessage(sysParam.getAiUserPrompt()));
		
		this.deepSeekClient.chatCompletions(model, messages, maxTokens, temperature, responseFormat, apiKey);
		
		while(true) {
			
		}
	}

	@Test
	void testGetUserBalance() {
		fail("Not yet implemented");
	}

}
