/*
 Navicat Premium Data Transfer

 Source Server         : ***************_23306
 Source Server Type    : MySQL
 Source Server Version : 50744
 Source Host           : ***************:23306
 Source Schema         : knife-manager

 Target Server Type    : MySQL
 Target Server Version : 50744
 File Encoding         : 65001

 Date: 02/04/2025 11:30:44
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for BorrowReturn
-- ----------------------------
DROP TABLE IF EXISTS `BorrowReturn`;
CREATE TABLE `BorrowReturn`  (
  `鍊熻繕缂栧彿` int(11) NOT NULL COMMENT '鍊熻繕缂栧彿',
  `鍊熻繕鐘舵€乣 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鍊熻繕鐘舵€?,
  `鍊熻繕浜篳 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鍊熻繕浜?,
  `鍊熻繕鍒€鍏风紪鍙穈 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鍊熻繕鍒€鍏风紪鍙?,
  `鍊熻繕鍒€鍏峰悕绉癭 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鍊熻繕鍒€鍏峰悕绉?,
  `鍊熻繕鍒€鍏峰睘鎬 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鍊熻繕鍒€鍏峰睘鎬?,
  `鍊熻繕鍒€鍏峰巶瀹禶 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鍊熻繕鍒€鍏峰巶瀹?,
  `鍊熻繕鏁伴噺` int(11) NULL DEFAULT NULL COMMENT '鍊熻繕鏁伴噺',
  `鏃ユ湡` datetime(0) NULL DEFAULT NULL COMMENT '鏃ユ湡',
  PRIMARY KEY (`鍊熻繕缂栧彿`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '鍊熻繕琛? ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of BorrowReturn
-- ----------------------------

-- ----------------------------
-- Table structure for Kit
-- ----------------------------
DROP TABLE IF EXISTS `Kit`;
CREATE TABLE `Kit`  (
  `閮ㄤ欢缂栧彿` int(11) NOT NULL COMMENT '閮ㄤ欢缂栧彿',
  `鍒€鍏风紪鍙穈 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鍒€鍏风紪鍙?,
  `閮ㄤ欢鍚嶇О` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '閮ㄤ欢鍚嶇О',
  `瑙勬牸` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '瑙勬牸',
  `澶囨敞` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '澶囨敞',
  PRIMARY KEY (`閮ㄤ欢缂栧彿`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '閰嶅閮ㄤ欢琛? ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of Kit
-- ----------------------------

-- ----------------------------
-- Table structure for KnifeAttribute
-- ----------------------------
DROP TABLE IF EXISTS `KnifeAttribute`;
CREATE TABLE `KnifeAttribute`  (
  `灞炴€х紪鍙穈 int(11) NOT NULL COMMENT '鍒€鍏峰睘鎬х紪鍙?,
  `鍒€鍏风紪鍙穈 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鍒€鍏风紪鍙?,
  `鍒€鍏峰悕绉癭 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鍒€鍏峰悕绉?,
  `鍒€鍏峰睘鎬 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鍒€鍏峰睘鎬?,
  PRIMARY KEY (`灞炴€х紪鍙穈) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '鍒€鍏峰睘鎬ц〃' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of KnifeAttribute
-- ----------------------------

-- ----------------------------
-- Table structure for KnifeInfo
-- ----------------------------
DROP TABLE IF EXISTS `KnifeInfo`;
CREATE TABLE `KnifeInfo`  (
  `鍒€鍏风紪鍙穈 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '鍒€鍏风紪鍙?,
  `鍒€鍏峰悕绉癭 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鍒€鍏峰悕绉?,
  `鍒€鍏峰睘鎬 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鍒€鍏峰睘鎬?,
  `鍒€鍏风敓浜у巶瀹禶 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '鍒€鍏风敓浜у巶瀹?,
  `鍒€鍏锋暟閲廯 int(11) NULL DEFAULT NULL COMMENT '鍒€鍏锋暟閲?,
  `鍒€鍏峰鍛絗 int(11) NULL DEFAULT NULL COMMENT '鍒€鍏峰鍛?,
  `鐢熶骇鏃ユ湡` datetime(0) NULL DEFAULT NULL COMMENT '鐢熶骇鏃ユ湡',
  PRIMARY KEY (`鍒€鍏风紪鍙穈) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '鍒€鍏蜂俊鎭綍鍏ヨ〃' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of KnifeInfo
-- ----------------------------

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` char(4) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '涓婚敭',
  `username` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '鐢ㄦ埛鍚?,
  `password` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '瀵嗙爜',
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE INDEX `uq_username`(`username`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '绯荤粺鐢ㄦ埛' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES ('1', 'admin', '888888');

SET FOREIGN_KEY_CHECKS = 1;
