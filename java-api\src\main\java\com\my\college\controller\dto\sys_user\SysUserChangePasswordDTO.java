package com.my.college.controller.dto.sys_user;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改密码
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUserChangePasswordDTO implements Serializable {

    private static final long serialVersionUID = 1L;


	@NotNull
    private String username;
	
	/**
	 * 密码
	 */
	@NotBlank(message="password不能为空")
	private String password;
	
	
}
