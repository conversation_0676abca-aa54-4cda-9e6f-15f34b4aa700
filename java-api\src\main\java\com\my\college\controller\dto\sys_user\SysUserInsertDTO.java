package com.my.college.controller.dto.sys_user;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_用户信息表
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUserInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 用户名
     */
	@NotNull(message="username不能为空")
    private String username;

    /**
     * 密码
     */
	@NotNull(message="password不能为空")
    private String password;
	
	/**
	 * 是否管理员
	 */
    private Boolean adminFlag;
    
    /**
     * 备注
     */
    private String remark;

}
