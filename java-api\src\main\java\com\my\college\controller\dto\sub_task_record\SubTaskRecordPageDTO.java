package com.my.college.controller.dto.sub_task_record;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.alibaba.fastjson2.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.college.controller.vo.sub_task_record.SubTaskRecordPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_子任务记录表
 *
 * <AUTHOR>
 * @date 2025-06-22
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SubTaskRecordPageDTO 
						extends PageDTO<SubTaskRecordPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 子任务编号
     */
    private String subTaskId;

    /**
     * 主任务编号
     */
    private String taskId;

    /**
     * 子任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 子任务结束时间
     */
    private LocalDateTime endTime;

    /**
     * 子任务完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 网址
     */
    private String url;

    /**
     * 子任务状态(RUN, FAIL, SUCCESS, REBORN)
     */
    private String status;

    /**
     * 子任务失败备注
     */
    private String failRemark;

}
