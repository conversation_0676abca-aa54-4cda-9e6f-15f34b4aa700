package com.my.college.controller;


import org.springframework.web.bind.annotation.RequestMapping;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import com.my.college.controller.dto.dept.DeptInsertDTO;
import com.my.college.controller.dto.dept.DeptPageDTO;
import com.my.college.controller.dto.dept.DeptUpdateDTO;
import com.my.college.controller.dto.dept.DeptDeleteDTO;
import com.my.college.controller.vo.dept.DeptDetailVO;
import com.my.college.controller.vo.dept.DeptPageVO;
import com.my.college.controller.vo.StdResp;

import com.my.college.service.DeptService;

import org.springframework.web.bind.annotation.RestController;

/**
 * 部门表 
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/api/dept")
@RequiredArgsConstructor
public class DeptController {

    private final DeptService deptService;

    /**
    * 新增
    */
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody DeptInsertDTO insertDTO) {
    	this.deptService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody DeptUpdateDTO updateDTO) {
    	this.deptService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<DeptDetailVO> detail(@PathVariable String id) {
    	return StdResp.success(this.deptService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<DeptPageVO>> page(DeptPageDTO pageDTO) {
        Page<DeptPageVO> page = this.deptService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody DeptDeleteDTO deleteDTO) {
    	this.deptService.delete(deleteDTO);
		return StdResp.success();
    }
    
}
