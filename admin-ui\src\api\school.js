import { reqGet, reqPut, reqDelete, reqPost, reqUpload } from './axiosFun';

// 分页
export const schoolPage = (params) => { return reqGet("/school/page", params) };

// 获取全部学校列表
export const schoolList = (params) => { return reqGet("/school/list", params) };

// 获取详情
export const schoolDetail = (params) => { return reqGet("/school/" + params.id) };

// 创建学校
export const createSchool = (params) => {return reqPost("/school", params);};

// 更新学校
export const updateSchool = (params) => {return reqPut("/school", params);};

// 删除学校
export const deleteSchool = (params) => { return reqDelete("/school", params) };

// 下载
export const schoolDownload = (params) => { return reqGet("/school/download", params) };

//  上传学校
export const schoolUpload = (params) => { return reqUpload("/school/upload", params) };