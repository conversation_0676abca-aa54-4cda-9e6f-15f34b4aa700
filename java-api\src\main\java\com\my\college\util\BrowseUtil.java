package com.my.college.util;

import java.lang.reflect.Method;

/**
 * @title java调用浏览器打开指定页面
 */
public class BrowseUtil {

	public static void browse(String url) throws Exception {
		String osName = System.getProperty("os.name", "");// 获取操作系统的名字

		if (osName.startsWith("Windows")) {// windows
			Runtime.getRuntime().exec("rundll32 url.dll,FileProtocolHandler " + url);
		} else if (osName.startsWith("Mac OS")) {// Mac
			Class fileMgr = Class.forName("com.apple.eio.FileManager");
			Method openURL = fileMgr.getDeclaredMethod("openURL", String.class);
			openURL.invoke(null, url);
		} else {// Unix or Linux
			String[] browsers = { "firefox", "opera", "konqueror", "epiphany", "mozilla", "netscape" };
			String browser = null;
			for (int count = 0; count < browsers.length && browser == null; count++) { // 执行代码，在brower有值后跳出，
				// 这里是如果进程创建成功了，==0是表示正常结束。
				if (Runtime.getRuntime().exec(new String[] { "which", browsers[count] }).waitFor() == 0) {
					browser = browsers[count];
				}
			}
			if (browser == null) {
				throw new RuntimeException("未找到任何可用的浏览器");
			} else {// 这个值在上面已经成功的得到了一个进程。
				Runtime.getRuntime().exec(new String[] { browser, url });
			}
		}
	}

}
