package com.my.college.service.impl;

import java.lang.reflect.Field;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.college.cache.SysParamCache;
import com.my.college.controller.dto.sys_param.SysParamUpdateDTO;
import com.my.college.controller.vo.sys_param.SysParamVO;
import com.my.college.enums.SysParamFieldEnum;
import com.my.college.exception.BusinessException;
import com.my.college.mybatis.entity.SysParam;
import com.my.college.mybatis.mapper.SysParamMapper;
import com.my.college.service.SysParamService;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 系统参数配置表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-05-01
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SysParamServiceImpl extends ServiceImpl<SysParamMapper, SysParam> implements SysParamService {

	private final SysParamCache sysParamCache;
	

	@Transactional
	@Override
	public void update(SysParamUpdateDTO updateDTO) {
		try {
			// 通过反射获取DTO中的非null字段值
			Class<?> dtoClass = updateDTO.getClass();
			
			// 遍历所有的参数枚举（排除id和batchNumber字段）
			for (SysParamFieldEnum fieldEnum : SysParamFieldEnum.values()) {
				String fieldName = fieldEnum.name();
				
				// 通过反射获取字段值
				Field field = dtoClass.getDeclaredField(fieldName);
				field.setAccessible(true);
				String fieldValue = (String) field.get(updateDTO);
				
				// 如果字段值不为null，则加入更新列表
				if (StrUtil.isNotBlank(fieldValue)) {
					// 检查数据库中是否已存在该键
					LambdaQueryWrapper<SysParam> queryWrapper = new LambdaQueryWrapper<SysParam>()
							.eq(SysParam::getK, fieldName);
					SysParam entity = this.baseMapper.selectOne(queryWrapper);
					if (entity == null) {
						log.error("SysParam表缺少k: {}", fieldName);
					}
					BusinessException.when(entity == null, "更新参数与数据库字段不一致");
					
					// 执行更新
					LambdaUpdateWrapper<SysParam> updateWrapper = new LambdaUpdateWrapper<SysParam>()
							.eq(SysParam::getK, fieldName)
							.set(SysParam::getV, fieldValue.toString());
					this.baseMapper.update(null, updateWrapper);
					log.info("更新系统参数: {} = {}", fieldName, fieldValue);
				}
			}
			log.info("系统参数批量更新完成");
		} catch (Exception e) {
			log.error("系统参数更新失败", e);
			throw new RuntimeException("系统参数更新失败", e);
		}
		
		// 重置缓存
		this.sysParamCache.reloadCache();
	}

	@Override
	public SysParamVO get() {
		return this.sysParamCache.get();
	}

	@Transactional
	@Override
	public Integer nextBatchNumber() {
		// 这个字侧不需要返回给SysParamVO
		LambdaQueryWrapper<SysParam> queryWrapper = new LambdaQueryWrapper<SysParam>()
				.eq(SysParam::getK, "batchNumber");
		SysParam entity = this.baseMapper.selectOne(queryWrapper);
		Integer batchNumber = Integer.parseInt(entity.getV());
		Integer nextBatchNumber = batchNumber + 1;
		
		// 更新数据库batchNumber+1
		entity.setV(nextBatchNumber.toString());
		this.baseMapper.updateById(entity);
		return nextBatchNumber;
	}

}
