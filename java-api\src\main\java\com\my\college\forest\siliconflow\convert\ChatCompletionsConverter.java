package com.my.college.forest.siliconflow.convert;

import java.lang.reflect.Type;

import com.alibaba.fastjson2.JSON;
import com.dtflys.forest.converter.text.DefaultTextConverter;
import com.dtflys.forest.utils.StringUtils;
import com.my.college.exception.BusinessException;
import com.my.college.forest.siliconflow.vo.ChatCompletionsVO;

/**
 * ResultConvert
 */
public class ChatCompletionsConverter extends DefaultTextConverter {

	
	@SuppressWarnings("unchecked")
	@Override
    public ChatCompletionsVO convertToJavaObject(String source, Type targetType) {
    	BusinessException.when(StringUtils.isBlank(source), "接口无数据");
    	
    	try {
    		ChatCompletionsVO vo = JSON.parseObject(source, ChatCompletionsVO.class);
    		return vo;
    	} catch(Exception e) {
    		return null;
    	}
	}
}
