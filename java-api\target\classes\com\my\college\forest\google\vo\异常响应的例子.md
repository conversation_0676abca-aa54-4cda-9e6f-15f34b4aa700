== 异常情况1：cx有误
{
    "error": {
        "code": 400,
        "message": "Request contains an invalid argument.",
        "errors": [
            {
                "message": "Request contains an invalid argument.",
                "domain": "global",
                "reason": "badRequest"
            }
        ],
        "status": "INVALID_ARGUMENT"
    }
}


== 异常情况2：API密钥有误
{
    "error": {
        "code": 400,
        "message": "API key not valid. Please pass a valid API key.",
        "errors": [
            {
                "message": "API key not valid. Please pass a valid API key.",
                "domain": "global",
                "reason": "badRequest"
            }
        ],
        "status": "INVALID_ARGUMENT",
        "details": [
            {
                "@type": "type.googleapis.com/google.rpc.ErrorInfo",
                "reason": "API_KEY_INVALID",
                "domain": "googleapis.com",
                "metadata": {
                    "service": "customsearch.googleapis.com"
                }
            },
            {
                "@type": "type.googleapis.com/google.rpc.LocalizedMessage",
                "locale": "en-US",
                "message": "API key not valid. Please pass a valid API key."
            }
        ]
    }
}

===== 异常情况3：免费账号超100次
{
  "error": {
    "code": 429,
    "message": "Quota exceeded for quota metric 'Queries' and limit 'Queries per day' of service 'customsearch.googleapis.com' for consumer 'project_number:75561874528'.",
    "errors": [
      {
        "message": "Quota exceeded for quota metric 'Queries' and limit 'Queries per day' of service 'customsearch.googleapis.com' for consumer 'project_number:75561874528'.",
        "domain": "global",
        "reason": "rateLimitExceeded"
      }
    ],
    "status": "RESOURCE_EXHAUSTED",
    "details": [
      {
        "@type": "type.googleapis.com/google.rpc.ErrorInfo",
        "reason": "RATE_LIMIT_EXCEEDED",
        "domain": "googleapis.com",
        "metadata": {
          "quota_location": "global",
          "service": "customsearch.googleapis.com",
          "consumer": "projects/75561874528",
          "quota_limit": "DefaultPerDayPerProject",
          "quota_limit_value": "100",
          "quota_metric": "customsearch.googleapis.com/requests",
          "quota_unit": "1/d/{project}"
        }
      },
      {
        "@type": "type.googleapis.com/google.rpc.Help",
        "links": [
          {
            "description": "Request a higher quota limit.",
            "url": "https://cloud.google.com/docs/quotas/help/request_increase"
          }
        ]
      }
    ]
  }
}



===== 异常情况4：收费账号超1万次
{
  "error": {
    "code": 403,
    "message": "Request throttled due to daily limit being reached.",
    "errors": [
      { "reason": "dailyLimitExceeded", …. }
    ],
    "status": "RESOURCE_EXHAUSTED"
  }
}
