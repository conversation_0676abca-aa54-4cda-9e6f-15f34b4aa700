package com.my.college.task.subtask;

import java.util.List;

import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.my.college.forest.markdown.dto.MarkdownDTO;

import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
class SubTaskProcessTest {

	@Autowired
	SubTaskProcess subTaskProcess;
	
	
	@Test
	void testAsyncMarkdown() {
		String[] urlArr = {
			"https://ppe.sas.upenn.edu/study/study-abroad/recommended-programs",
			"https://almanac.upenn.edu/volume-65-number-34",
			"https://be.seas.upenn.edu/undergraduate/international-opportunities/",
			"https://oie.jhu.edu/sexual-violence-advisory-committee-svac/",
			"https://jobs.jhu.edu/job/Baltimore-Associate-Athletic-Director%2C-Compliance-MD-21218/**********/",
			"https://wellbeing.jhu.edu/blog/2024/08/21/the-red-zone-of-sexual-assault-3/",
			"https://news.northwestern.edu/stories/2016/05/founder-director-of-northwestern-study-abroad-office-to-retire/",
			"https://wellbeing.jhu.edu/blog/2022/11/23/how-to-promote-healing-with-physical-trauma-informed-practices/",
			"https://undergradresearch.northwestern.edu/2015/06/17/moving-in/",
			"https://wellbeing.jhu.edu/blog/2022/09/02/the-red-zone-of-sexual-assault/",
			"https://buffett.northwestern.edu/about/staff/bios/jessica-fetridge.html",
			"https://wccias.northwestern.edu/events/past-events.html",
			"https://oie.fas.harvard.edu/people/role/oie-staff",
			"https://oie.fas.harvard.edu/about",
			"https://oie.fas.harvard.edu/people/nicole-garcia",
			"https://oie.fas.harvard.edu/people/role/concentration-advisers",
			"https://chemistry.princeton.edu/undergrads/outside-course-approval/",
			"https://mitsloan.mit.edu/staff/directory/elizabeth-galvin-orozco",
			"https://oie.fas.harvard.edu/oie-advisers",
			"https://yehcollege.princeton.edu/people/faculty-and-fellows/fellows",
			"https://oue.fas.harvard.edu/staff",
			"https://undergrad.psychology.fas.harvard.edu/study-out-residence",
			"https://opportunities.columbia.edu/jobs/associate-director-investigations-faculty-and-staff-anti-discrimination-and-discriminatory-harassment-division-morningside-new-york-united-states",
			"https://study-abroad.uchicago.edu/people/kylie-poulin-zahora",
			"https://hr.fas.harvard.edu/people",
			"https://global.undergrad.columbia.edu/program/kyoto-consortium-summer-classical-japanese-kcjs",
			"https://ceas.uchicago.edu/korean-studies-funding",
			"https://ppe.sas.upenn.edu/study/study-abroad/recommended-programs",
			"https://advising.college.harvard.edu/about-us/",
			"https://www.gs.columbia.edu/content/university-policies",
			"https://college.uchicago.edu/navigating-college",
			"https://almanac.upenn.edu/volume-65-number-34",
			"https://sociology.fas.harvard.edu/refugee-fieldwork-program",
			"https://global.undergrad.columbia.edu/program/kyoto-consortium-summer-modern-japanese-kcjs",
			"https://ceas.uchicago.edu/japanese-studies-funding",
			"https://be.seas.upenn.edu/undergraduate/international-opportunities/",
			"https://oie.jhu.edu/sexual-violence-advisory-committee-svac/",
			"https://goaskalice.columbia.edu/answered-questions/what-should-i-do-if-im-pregnant-where-abortion-isnt-legal",
			"https://ceas.uchicago.edu/chinese-studies-funding",
			"https://jobs.jhu.edu/job/Baltimore-Associate-Athletic-Director%2C-Compliance-MD-21218/**********/",
			"https://iri.columbia.edu/healthclimate2016/speakers-panelists/",
			"https://news.northwestern.edu/stories/2016/05/founder-director-of-northwestern-study-abroad-office-to-retire/",
			"https://wellbeing.jhu.edu/blog/2024/08/21/the-red-zone-of-sexual-assault-3/",
			"https://undergradresearch.northwestern.edu/2015/06/17/moving-in/",
			"https://wellbeing.jhu.edu/blog/2022/11/23/how-to-promote-healing-with-physical-trauma-informed-practices/",
			"https://buffett.northwestern.edu/about/staff/bios/jessica-fetridge.html",
			"https://wellbeing.jhu.edu/blog/2022/09/02/the-red-zone-of-sexual-assault/",
			"https://www.cmu.edu/oie/about/staff.html",
			"https://wccias.northwestern.edu/events/past-events.html",
			"https://www.cmu.edu/oie/about/bios/menand.html",
			"https://record.umich.edu/articles/new-panel-of-faculty-staff-students-will-advise-title-ix-coordinator/",
			"https://www.cmu.edu/oie/about/bios/van-rheenen.html",
			"https://lsa.umich.edu/sweetland/graduates/courses.html",
			"https://www.cmu.edu/oie/about/bios/gruzdeva.html",
			"https://lsa.umich.edu/honors/current-students/academic-information/honors-academic-board/grade-grievance.html",
			"https://oie.gatech.edu/staff",
			"https://www.cmu.edu/oie/community-connections/friday-email/fall-2021/03dec2021.html",
			"https://careers.umich.edu/job_detail/264882/graduate-student-instructor-graduate-writing-consultant",
			"https://ea.oie.gatech.edu/sac",
			"https://www.cmu.edu/hub/contact/staff.html",
			"https://www.engr.washington.edu/about/staff_directory",
			"https://diversity.umich.edu/resources/accessible-and-inclusive-resource-guide-for-events/",
			"https://isss.oie.gatech.edu/content/undergraduate-exchange-application",
			"https://engineering.cmu.edu/directory/bios/currin-lisa.html",
			"https://www.washington.edu/globalaffairs/2024/05/13/meet-courtney-kroll-2024-uw-excellence-in-global-engagement-award-recipient/",
			"https://internationalcenter.umich.edu/about/history",
			"https://policylibrary.gatech.edu/node",
			"https://www.cmu.edu/pre-college/admission/international-applicants.html",
			"https://www.washington.edu/alumni/travel/meet-the-team/",
			"https://research.umich.edu/research-at-michigan/diversity-equity-and-inclusion/fiveyearsummary/",
			"https://cos.gatech.edu/undergraduate-studies-neuroscience",
			"https://www.cmu.edu/ini/admissions/international_guide.html",
			"https://jsis.washington.edu/people/staff/",
			"https://diversity.umich.edu/dei-strategic-plan/dei-1-0-archives/unit-summaries/",
			"https://oue.gatech.edu/staff-directory",
			"https://www.cmu.edu/student-success/about/bios/michael-p.html",
			"https://www.engr.washington.edu/current/academics/student-academic-services-team",
			"https://staffcouncil.gatech.edu/council-members",
			"https://www.engr.washington.edu/mycoe/hr/students/student-performance-evaluations",
			"https://iac.gatech.edu/news/item/669805/delta-lines-sponsors-passports-georgia-tech-students",
			"https://steinhardt.nyu.edu/degree/ma-international-education/alumni-profiles",
			"https://www.engr.washington.edu/industry/team",
			"https://www.gatech.edu/news/2025/02/06/summer-study-abroad-deadlines-approaching",
			"https://www.engr.washington.edu/industry/capstone",
			"https://comm.gatech.edu/resources/campus-communications/contacts",
			"https://www.bu.edu/usc/leaveandwithdrawal/leave-external-study-abroad/",
			"https://www.engr.washington.edu/news/article/2019-05-09/summer-career-prep",
			"https://www.bu.edu/articles/2007/london-calling/",
			"https://www.engr.washington.edu/mycoe/staff/managing/perf_management.html",
			"https://www.bu.edu/abroad/about/advisor-resources/",
			"https://global.unc.edu/our-work/global-partnerships/global-partnership-awards/expansion-grant/",
			"https://www.bu.edu/articles/2023/learn-how-you-can-study-abroad/",
			"https://global.unc.edu/our-work/global-partnerships/global-partnership-awards/exploration-grant-application/",
			"https://www.bu.edu/usc/faqs/",
			"https://global.unc.edu/about/our-team/",
			"https://www.bu.edu/articles/2021/terriers-running-boston-marathon-for-charity/",
			"https://pharmacy.utexas.edu/news/maniruzzaman-lab-collaboration-works-develop-patient-focused-medicines-more-efficiently",
			"https://asia.unc.edu/about/people/",
			"https://www.bu.edu/globalprograms/discover/news/newsletters/spring-2017/remembering-bill-linsman/",
			"https://catalog.utexas.edu/general-information/coursesatoz/b-a/",
			"https://jobs.wisc.edu/jobs/iap-associate-director-for-program-management-madison-wisconsin-united-states",
			"https://global.unc.edu/our-work/global-partnerships/",
			"https://repositories.lib.utexas.edu/bitstreams/54588638-8c4c-45f3-b0b2-05822ff5301f/download",
			"https://studyabroad.wisc.edu/staff/lochner-atkinson-susan/",
			"https://global.unc.edu/our-work/global-partnerships/protocol-for-international-visits/",
			"https://repositories.lib.utexas.edu/bitstreams/0d6d9e56-3e43-4630-9e55-c8ae6ca144a5/download",
			"https://honors.ls.wisc.edu/study-abroad-3/",
			"https://global.unc.edu/about/our-team/katie-costanza/",
			"https://repositories.lib.utexas.edu/bitstreams/3768f07e-9895-472d-8cec-62ac01704c60/download",
			"https://studyabroad.wisc.edu/exchange/information-for-partner-universities/",
			"https://global.unc.edu/news-story/new-associate-director-of-global-education/",
			"https://haas.berkeley.edu/ewmba/contacts/",
			"https://studyabroad.wisc.edu/handbook/study-abroad-policies/",
			"https://global.unc.edu/about/our-team/melissa-mcmurray/",
			"https://nature.berkeley.edu/advising/office-instruction-and-student-affairs",
			"https://education.wisc.edu/news/class-notes-winter-2023-24-learning-connections/",
			"https://global.unc.edu/our-work/global-partnerships/international-agreements/",
			"https://nature.berkeley.edu/advising/meet-rausser-advisors",
			"https://directory.caltech.edu/departmental_directory/department/student-affairs-administration",
			"https://engineering.berkeley.edu/podcast/ess-808-study-abroad-as-a-berkeley-engineer/",
			"https://equalopportunity.yale.edu/meet-staff",
			"http://matrix.berkeley.edu/article-type/news/",
			"https://studyabroad.yale.edu/about-us/staff-list",
			"https://www.engineering.cornell.edu/office-of-inclusive-excellence/oie-who-we-are/",
			"https://faculty.studyabroad.yale.edu/resources/incidents-abroad",
			"https://cals.cornell.edu/news/2021/08/julie-ficarra-expanding-engaged-cross-cultural-learning-global-development",
			"https://funding.yale.edu/connect/fellowships-staff",
			"https://www.vet.cornell.edu/animal-health-diagnostic-center/testing-laboratories/serology-immunology",
			"https://catalog.yale.edu/ycps/yale-college/special-academic-resources/",
			"https://www.vet.cornell.edu/animal-health-diagnostic-center/testing-laboratories/virology",
			"https://faculty.studyabroad.yale.edu/running-program/proposing-program",
			"https://medicaleducation.weill.cornell.edu/student-resources/sexual-misconduct-campus-security",
			"https://oie.jhu.edu/sexual-violence-advisory-committee-svac/",
			"https://policy.cornell.edu/policy-library/consensual-relationships",
			"https://jobs.jhu.edu/job/Baltimore-Associate-Athletic-Director%2C-Compliance-MD-21218/**********/",
			"https://www.vet.cornell.edu/research/martin-gilbert",
			"https://wellbeing.jhu.edu/blog/2024/08/21/the-red-zone-of-sexual-assault-3/",
			"https://news.cornell.edu/stories/2001/11/dr-alfonso-torres-named-director-veterinary-diagnostic-laboratory",
			"https://wellbeing.jhu.edu/blog/2022/11/23/how-to-promote-healing-with-physical-trauma-informed-practices/",
			"https://wellbeing.jhu.edu/blog/2022/09/02/the-red-zone-of-sexual-assault/",
		};
		String taskId = "JUNIT-TASK";
		List<String> list = Lists.newArrayList(urlArr);
		list.stream().parallel().forEach(url -> {
			this.doMarkdown(taskId, url);
		});
		while (true) {
			
		}
	}
	
	private void doMarkdown(String taskId, String url) {
		String subTaskId = IdUtil.getSnowflakeNextIdStr();
		SubTaskContext.set(taskId, subTaskId, url);
		
		MarkdownDTO markdownDTO = new MarkdownDTO(url, 60);
		this.subTaskProcess.asyncProcess(markdownDTO);
	}

}
