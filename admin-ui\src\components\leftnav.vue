<template>
  <el-menu default-active="2" :collapse="collapsed" collapse-transition router :default-active="$route.path" unique-opened
    class="el-menu-vertical-demo" background-color="#334157" text-color="#fff" active-text-color="#ffd04b">
    <div class="logobox" style="color:white">
      <!-- <img class="logoimg" src="../assets/img/logo.png" alt=""> -->
      知名大学通讯录管理系统
    </div>
    <el-submenu v-for="menu in allmenu" :key="menu.menuid" :index="menu.menuname">
      <template slot="title">
        <i class="iconfont" :class="menu.icon"></i>
        <span>{{ menu.menuname }}</span>
      </template>
      <el-menu-item-group>
        <template v-for="chmenu in menu.menus">
          <!-- 内部路由菜单 -->
          <el-menu-item v-if="!chmenu.isExternal" :index="'/' + chmenu.url" :key="'menu-' + chmenu.menuid">
            <i class="iconfont" :class="chmenu.icon"></i>
            <span>{{ chmenu.menuname }}</span>
          </el-menu-item>
          <!-- 外部链接菜单 -->
          <el-menu-item v-else :index="chmenu.url" :key="'external-' + chmenu.menuid" @click.native="handleExternalLink(chmenu.externalLink)">
            <i class="iconfont" :class="chmenu.icon"></i>
            <span>{{ chmenu.menuname }}</span>
          </el-menu-item>
        </template>
      </el-menu-item-group>
    </el-submenu>
  </el-menu>
</template>
<script>
export default {
  name: 'leftnav',
  data() {
    return {
      collapsed: false,
      allmenu: [],
      userEntity: undefined
    }
  },
  created() {
    // 加载用户缓存
    this.userEntity = JSON.parse(localStorage.getItem('userEntity'));
    this.allmenu = this.getMenu()
    this.$root.Bus.$on('toggle', value => {
      this.collapsed = !value
    })
  },
  methods:{
    // 处理外部链接点击
    handleExternalLink(url) {
      window.open(url, '_blank')
    },
    // 查询菜单
    getMenu(){
      let menuArr = [
        {
          menuid: 1, icon: 'icon-cus-manage', menuname: '用户管理', hasThird: null, url: null,
          menus: [
            { menuid: 51, icon: 'icon-cus-manage', menuname: '用户管理', hasThird: 'N', url: 'sysUser', menus: null },
            // { menuid: 25, icon: 'icon-cat-skuInfo', menuname: '图标调示', hasThird: 'N', url: 'iconDisplay', menus: null },
          ],
        },
        {
          menuid: 2, icon: 'li-icon-xiangmuguanli', menuname: '爬虫管理', hasThird: null, url: null,
          menus: [
            { menuid: 12, icon: 'icon-cus-manage', menuname: '部门管理', hasThird: 'N', url: 'position', menus: null },
            { menuid: 13, icon: 'icon-cus-manage', menuname: '职位管理', hasThird: 'N', url: 'position', menus: null },
            { menuid: 14, icon: 'icon-cms-manage', menuname: 'Google参数管理', hasThird: 'N', url: 'googleParam', menus: null },
            { menuid: 11, icon: 'icon-cms-manage', menuname: '爬虫配置', hasThird: 'N', url: 'sysParam', menus: null },
            { menuid: 21, icon: 'icon-cat-skuQuery', menuname: '爬虫任务', hasThird: 'N', url: 'taskRecord', menus: null },
          ],
        },
        {
            menuid: 3, icon: 'li-icon-zhifuguanli', menuname: '数据管理', hasThird: null, url: null,
            menus: [
              { menuid: 31, icon: 'li-icon-xitongguanli', menuname: '美国省份', hasThird: 'N', url: 'province', menus: null },
              { menuid: 32, icon: 'icon-cat-skuInfo', menuname: '学校', hasThird: 'N', url: 'school', menus: null },
              { menuid: 33, icon: 'li-icon-zhifuguanli', menuname: '通讯录', hasThird: 'N', url: 'addressBook', menus: null },
          ],
        },
      ];

      // 根据adminFlag过滤菜单
      if (this.userEntity && !this.userEntity.adminFlag) {
        // 如果不是管理员，只显示学校管理和通讯录管理
        menuArr = menuArr.filter(menu => menu.menuid === 4 || menu.menuid === 3);
      }

      return menuArr;
    }
  }
}
</script>
<style>
.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 200px;
  min-height: 400px;
}

.el-menu-vertical-demo:not(.el-menu--collapse) {
  border: none;
  text-align: left;
}

.el-menu-item-group__title {
  padding: 0px;
}

.el-menu-bg {
  background-color: #1f2d3d !important;
}

.el-menu {
  border: none;
}

.logobox {
  height: 20px;
  line-height: 40px;
  color: #9d9d9d;
  font-size: 16px;
  text-align: center;
  padding: 15px 12% 20px 12%;
}

.logoimg {
  height: 40px;
}

.external-link-item {
  padding: 0 20px;
  font-size: 14px;
  color: #fff;
  height: 56px;
  line-height: 56px;
  cursor: pointer;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.external-link-item:hover {
  color: #ffd04b;
  background-color: #263445;
}
</style>
