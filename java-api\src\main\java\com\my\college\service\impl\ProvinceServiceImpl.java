package com.my.college.service.impl;

import com.my.college.mybatis.entity.Province;
import com.my.college.mybatis.mapper.ProvinceMapper;
import com.my.college.service.ProvinceService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.college.controller.dto.province.ProvinceInsertDTO;
import com.my.college.controller.dto.province.ProvincePageDTO;
import com.my.college.controller.dto.province.ProvinceUpdateDTO;
import com.my.college.controller.dto.province.ProvinceDeleteDTO;
import com.my.college.controller.vo.province.ProvinceDetailVO;
import com.my.college.controller.vo.province.ProvincePageVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 美国50个州州名 服务实现类
 *
 * <AUTHOR>
 * @date 2025-05-25
 */
@Service
public class ProvinceServiceImpl extends ServiceImpl<ProvinceMapper, Province> implements ProvinceService {


	@Transactional
	@Override
	public void insert(ProvinceInsertDTO insertDTO) {
		Province entity = BeanUtil.copyProperties(insertDTO, Province.class);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(ProvinceUpdateDTO updateDTO) {
		Province entity = BeanUtil.copyProperties(updateDTO, Province.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public ProvinceDetailVO detail(String id) {
		Province entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, ProvinceDetailVO.class);
	}

	@Override
	public Page<ProvincePageVO> page(ProvincePageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(ProvinceDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}	

	@Override
	public Province findByIdOrName(String value) {
		if (StrUtil.isBlank(value)) {
			return null;
		}
		
		LambdaQueryWrapper<Province> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(Province::getProvinceId, value)
				.or()
				.eq(Province::getChineseName, value);
		
		return this.getOne(queryWrapper);
	}
}
