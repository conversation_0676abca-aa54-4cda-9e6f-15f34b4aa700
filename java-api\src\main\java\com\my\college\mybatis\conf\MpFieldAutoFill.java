package com.my.college.mybatis.conf;

import java.time.LocalDateTime;

import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;

/**
 * Mybatis-plus自动填充
 */
@Component
public class MpFieldAutoFill implements MetaObjectHandler {

    /**
     * 创建时间
     */
    private static final String CREATE_TIME_FIELD = "createTime";
    
    /**
     * 是否可用
     */
    private static final String ENABLE_FIELD = "enable";
    
    /**
     * 修改时间
     */
    private static final String UPDATE_TIME_FIELD = "updateTime";
    

    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, CREATE_TIME_FIELD, LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, ENABLE_FIELD, Boolean.class, true);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, UPDATE_TIME_FIELD, LocalDateTime.class, LocalDateTime.now());
    }
    
}
