package com.my.college.controller.dto.position;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_职位表
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class PositionInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 职位
     */
    @NotBlank
    private String position;
    
}
