package com.my.college.service.check.impl;

import java.lang.reflect.Field;

import com.my.college.service.check.AbstractCheck;
import com.my.college.service.check.result.CheckResult;

import cn.hutool.core.util.StrUtil;

/**
 * AI超时检测器
 * <AUTHOR>
 */
public class AiTimeoutCheck extends AbstractCheck {

    @Override
    protected CheckResult doCheck(Object fieldValue, Field field, Object obj) {
    	String str = (String) fieldValue;
    	if (StrUtil.isBlank(str)) {
    		return fail(this.fieldLabel + "不能为空");
    	}
    	
        Integer aiTimeout = new Integer(str);
        if (aiTimeout <= 0) {
            return fail(this.fieldLabel + "必须大于零");
        }
        return success();
    }
    
//	/**
//	 * 检查DeepSeek AI密钥是否有效
//	 */
//	private CheckResult checkAiApiKey(String aiTimeout) {
//		if (StringUtils.isBlank(aiTimeout)) {
//			return new CheckResult(false, "AI接口秘钥", "AI密钥不能为空");
//		}
//		
//		try {
//			// 直接调用DeepSeek API查询用户余额
//			Map<String, Object> balanceResponse = deepSeekClient.getUserBalance(aiTimeout);
//			
//			if (balanceResponse != null && balanceResponse.containsKey("is_available")) {
//				@SuppressWarnings("unchecked")
//				List<Map<String, Object>> balanceInfos = (List<Map<String, Object>>) balanceResponse.get("balance_infos");
//				if (balanceInfos != null && !balanceInfos.isEmpty()) {
//					Map<String, Object> balanceInfo = balanceInfos.get(0);
//					// String totalBalance = (String) balanceInfo.get("total_balance");
//					// String grantedBalance = (String) balanceInfo.get("granted_balance");
//					boolean isAvailable = (boolean) balanceResponse.get("is_available");
//					String toppedUpBalance = (String) balanceInfo.get("topped_up_balance");
//					return new CheckResult(isAvailable, "AI接口秘钥", String.format("余额: %s 元", toppedUpBalance));
//				}
//			}
//			
//			// 如果获取余额成功但没有详细信息，返回AI密钥有效的信息
//			return new CheckResult(false, "AI接口秘钥", "AI密钥有效, 但查询余额失败");
//		} catch (Exception e) {
//			String errorMessage = e.getMessage();
//			// 检查是否包含认证失败信息
//			if (errorMessage != null && errorMessage.contains("Authentication Fails")) {
//				return new CheckResult(false, "AI接口秘钥", "DeepSeek API_KEY不正确");
//			}
//			// 账号余额不足
//			else if (errorMessage != null && errorMessage.contains("Insufficient Balance")) {
//				return new CheckResult(false, "AI接口秘钥", "DeepSeek账号余额不足");
//			}
//			return new CheckResult(false, "AI接口秘钥", errorMessage);
//		}
//	}
}
