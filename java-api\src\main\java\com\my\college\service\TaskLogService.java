package com.my.college.service;

import java.util.List;

import org.springframework.boot.logging.LogLevel;

import com.baomidou.mybatisplus.extension.service.IService;
import com.my.college.controller.vo.task_log.TaskLogDetailVO;
import com.my.college.mybatis.entity.TaskLog;

/**
 * 任务日志表 服务类
 *
 * <AUTHOR>
 * @date 2025-05-03
 */
public interface TaskLogService extends IService<TaskLog> {

	
	/**
	 * 通过taskId查询所有日志，倒序
	 * @param taskId 任务ID
	 * @return 日志列表
	 */
	List<TaskLogDetailVO> list(String taskId);

	/**
	 * 保存日志
	 * @param level
	 * @param content
	 */
	void save(LogLevel level, String content);
}
