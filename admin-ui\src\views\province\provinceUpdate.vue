<template>
  <!-- 修改 -->
  <el-dialog title="修改美国省份" :visible.sync="editFormVisible" width="500px" @click="closeDialog">
    <el-form label-width="100px" :model="editForm" :rules="rules" ref="editForm">
      <el-form-item label="省份编号" prop="provinceId">
        {{ editForm.provinceId }}
      </el-form-item>
      <el-form-item label="英文名称" prop="englishName">
        <el-input v-model="editForm.englishName" placeholder="请输入英文名称"></el-input>
      </el-form-item>
      <el-form-item label="中文名称" prop="chineseName">
        <el-input v-model="editForm.chineseName" placeholder="请输入中文名称"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="submitForm('editForm')">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateProvince } from '../../api/province'

export default {
  name: 'provinceUpdate',
  props: ['childMsg'],
  data() {
    return {
      loading: false,
      editFormVisible: false,
      editForm: {
        provinceId: '',
        englishName: '',
        chineseName: ''
      },
      rules: {
        englishName: [
          { required: true, message: '请输入英文名称', trigger: 'blur' },
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ],
        chineseName: [
          { required: true, message: '请输入中文名称', trigger: 'blur' },
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    submitForm(editData) {
      let self = this;
      this.$refs[editData].validate(valid => {
        if (!valid) {
          return false;
        }
        self.loading = true;
        updateProvince(this.editForm)
          .then(res => {
            self.editFormVisible = false;
            self.loading = false;
            self.$emit('callback');
            self.$message({ type: 'success', message: '修改成功' });
          })
          .catch(err => {
            self.loading = false;
            self.$message.error('修改失败：' + err.message);
          });
      });
    },
    closeDialog() {
      this.editFormVisible = false;
    },
    show(row) {
      this.editFormVisible = true;
      this.loading = false;
      this.resetForm();
      if (row) {
        Object.assign(this.editForm, row);
      }
    },
    resetForm() {
      this.editForm = {
        provinceId: '',
        englishName: '',
        chineseName: ''
      };
      if (this.$refs.editForm) {
        this.$refs.editForm.resetFields();
      }
    }
  }
}
</script>

<style>
.el-dialog .el-dialog__body {
  padding: 0px 20px !important;
}
</style>
