package com.my.college.task;

/**
 * 任务上下文
 */
public class TaskContext {
	
    private static final ThreadLocal<String> TASK_ID_LOCAL = new ThreadLocal<String>();
    private static final ThreadLocal<Integer> BATCH_NUMBER_LOCAL = new ThreadLocal<Integer>();
    private static final ThreadLocal<Integer> SCHOOL_ID_LOCAL = new ThreadLocal<Integer>();

    
    /**
     * 设置批次号、任务id、学校id
     * @param batchNumber
     * @param taskId
     * @param schoolId
     */
    public static void set(Integer batchNumber, String taskId, Integer schoolId) {
    	BATCH_NUMBER_LOCAL.set(batchNumber);
    	TASK_ID_LOCAL.set(taskId);
    	SCHOOL_ID_LOCAL.set(schoolId);
    }    
    
    /**
     * 设置批次号、任务id (兼容旧版本)
     * @param batchNumber
     * @param taskId
     */
    public static void set(Integer batchNumber, String taskId) {
    	BATCH_NUMBER_LOCAL.set(batchNumber);
    	TASK_ID_LOCAL.set(taskId);
    }    
    
    /**
     * 清除批次号、任务id、学校id
     * 防止内存泄漏，在异步任务结束时调用
     */
    public static void remove() {
    	BATCH_NUMBER_LOCAL.remove();
    	TASK_ID_LOCAL.remove();
    	SCHOOL_ID_LOCAL.remove();
    }
    
    /**
     * 获取批次号
     * @return
     */
    public static Integer getBatchNumber() {
    	return BATCH_NUMBER_LOCAL.get();
    }

    /**
     * 获取任务id
     * @return
     */
    public static String getTaskId() {
    	return TASK_ID_LOCAL.get();
    }

    /**
     * 获取学校id
     * @return
     */
    public static Integer getSchoolId() {
    	return SCHOOL_ID_LOCAL.get();
    }

}
