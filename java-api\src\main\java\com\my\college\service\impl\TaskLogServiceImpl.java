package com.my.college.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.boot.logging.LogLevel;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.college.controller.vo.task_log.TaskLogDetailVO;
import com.my.college.mybatis.entity.TaskLog;
import com.my.college.mybatis.mapper.TaskLogMapper;
import com.my.college.service.TaskLogService;
import com.my.college.task.TaskContext;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务日志表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-05-03
 */
@Service
@Slf4j
public class TaskLogServiceImpl extends ServiceImpl<TaskLogMapper, TaskLog> implements TaskLogService {

	/**
	 * 记录任务日志
	 * @param content 日志内容
	 */
	@Override
	public void save(LogLevel level, String content) {
		// 写日志文件
		switch (level) {
			case INFO:
				log.info("[{}/{}/{}] {}", TaskContext.getBatchNumber(), TaskContext.getTaskId(), TaskContext.getSchoolId(), content);
				break;
			case WARN:
				log.warn("[{}/{}/{}] {}", TaskContext.getBatchNumber(), TaskContext.getTaskId(), TaskContext.getSchoolId(), content);
				break;
			case ERROR:
				log.error("[{}/{}/{}] {}", TaskContext.getBatchNumber(), TaskContext.getTaskId(), TaskContext.getSchoolId(), content);
				break;
			default:
				break;
		}
		// 写表
		TaskLog logDTO = TaskLog.builder()
				.taskId(TaskContext.getTaskId())
				.type(level.name())
				.content(content)
				.createTime(LocalDateTime.now())
				.build();
		this.save(logDTO);
	}
	
	@Override
	public List<TaskLogDetailVO> list(String taskId) {
		// 倒序查询
		LambdaQueryWrapper<TaskLog> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(TaskLog::getTaskId, taskId)
				   .orderByDesc(TaskLog::getLogId);
		
		List<TaskLog> taskLogList = this.baseMapper.selectList(queryWrapper);
		
		return taskLogList.stream()
				.map(taskLog -> BeanUtil.copyProperties(taskLog, TaskLogDetailVO.class))
				.collect(Collectors.toList());
	}
}
