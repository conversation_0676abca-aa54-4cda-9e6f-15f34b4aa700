package com.my.college.forest.google;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson2.JSON;
import com.my.college.forest.google.dto.CustomSearchDTO;
import com.my.college.forest.google.onerror.CustomSearchOnError;
import com.my.college.forest.google.vo.CustomSearchVO;

import lombok.extern.slf4j.Slf4j;


@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
class GoogleClientTest {
	
	@Autowired
	GoogleClient googleClient;

	
	@Test
	void testCustomSearch1() {
		String key = "AIzaSyAr8GuQumINcVD9ama7aMmmPJvyJCQBW5g";
		String cx = "0720b11a8e7594412~!@";
		String q = "site:harvard.edu intext:(\"study abroad director\" OR \"associate director\" OR \"study abroad coordinator\") (intext:\"@harvard.edu\" OR intext:\"email\" OR intext:\"contact\") intitle:(\"staff\" OR \"contact\" OR \"directory\" OR \"people\") intext:(\"office of international education\" OR \"OIE\" OR \"study abroad office\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf";
		Integer start = 1;
		Integer num = 10;
		
		doRequest(key, cx, q);
		log.info("finish");
	}

	@Test
	void testCustomSearch2() {
		String key = "AIzaSyAr8GuQumINcVD9ama7aMmmPJvyJCQBW5g~~"; 
		String cx = "0720b11a8e7594412";
		String q = "site:harvard.edu intext:(\"study abroad director\" OR \"associate director\" OR \"study abroad coordinator\") (intext:\"@harvard.edu\" OR intext:\"email\" OR intext:\"contact\") intitle:(\"staff\" OR \"contact\" OR \"directory\" OR \"people\") intext:(\"office of international education\" OR \"OIE\" OR \"study abroad office\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf";
		Integer start = 1;
		Integer num = 10;
		
		this.doRequest(key, cx, q);
		log.info("finish");
	}
	
	
	private void doRequest(String key, String cx, String q) {
		CustomSearchOnError onError = new CustomSearchOnError();
		CustomSearchDTO dto = new CustomSearchDTO(key, cx, q);
		CustomSearchVO vo = this.googleClient.customSearch(dto, onError);
		if (onError.isError()) {
			log.info(onError.getErrMsg());
		} else {
			log.info(JSON.toJSONString(vo));
		}
	}

}
