package com.my.college.forest.siliconflow.dto.component;

import lombok.Data;

@Data
public class Message {
	
	private String role;
	
	private String content;
	
	
	private Message() {
	}

	private Message(String role, String content) {
		super();
		this.role = role;
		this.content = content;
	}
	
	/**
	 * 系统消息
	 * @param content
	 * @return
	 */
	public static Message system(String content) {
		return new Message("system", content);
	}
	
	/**
	 * 用户消息
	 * @param content
	 * @return
	 */
	public static Message user(String content) {
		return new Message("user", content);
	}

	
}
