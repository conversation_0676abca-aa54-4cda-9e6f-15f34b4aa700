// 导入组件
import Vue from 'vue';
import Router from 'vue-router';

import login from '@/views/login';
import index from '@/views/index';

import MyPasswordEdit from '@/views/My/MyPasswordEdit';

import taskRecord from '@/views/taskRecord/taskRecord';
import taskRecordDetail from '@/views/taskRecord/taskRecordDetail';
import batchTaskCreate from '@/views/taskRecord/batchTaskCreate';

import sysParam from '@/views/sysParam/sysParam';

import iconDisplay from '@/views/iconfont/iconDisplay';

import addressBook from '@/views/addressBook/addressBook';

import school from '@/views/school/school';

import province from '@/views/province/province';

import position from '@/views/position/position';

import googleParam from '@/views/googleParam/googleParam';

import sysUser from '@/views/sysUser/sysUser';

// router enable
Vue.use(Router);

// export router
export default new Router({
    routes: [
        {path:'/', name:'', component:login, hidden:true, meta:{requireAuth:false}},
        {path:'/login', name:'登录', component:login, hidden:true, meta:{requireAuth:false}},
        {path:'/index', name:'首页', component:index, iconCls:'el-icon-tickets', children:[

            {path:'/MyPasswordEdit', name:'MyPasswordEdit', component:MyPasswordEdit, meta:{requireAuth:true}},

            {path:'/taskRecord', name:'任务记录管理', component:taskRecord, meta:{requireAuth:true}},
            {path:'/taskRecordDetail', name:'任务详情', component:taskRecordDetail, meta:{requireAuth:true}},
            {path:'/batchTaskCreate', name:'批量创建任务', component:batchTaskCreate, meta:{requireAuth:true}},

            {path:'/sysParam', name:'系统参数配置', component:sysParam, meta:{requireAuth:true}},

            {path:'/province', name:'省份管理', component:province, meta:{requireAuth:true}},

            {path:'/position', name:'常用职位', component:position, meta:{requireAuth:true}},

            {path:'/googleParam', name:'Google参数管理', component:googleParam, meta:{requireAuth:true}},

            {path:'/iconDisplay', name:'图标展示', component:iconDisplay, meta:{requireAuth:true}},

            {path:'/addressBook', name:'通讯录', component:addressBook, meta:{requireAuth:true}},

            {path:'/school', name:'学校', component:school, meta:{requireAuth:true}},

            {path:'/sysUser', name:'用户管理', component:sysUser, meta:{requireAuth:true}},
        ]}]
})
