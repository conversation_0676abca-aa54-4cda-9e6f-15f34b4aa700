package com.my.college.task.subtask;

import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
class SubTaskEntranceTest {
	
	@Autowired
	SubTaskEntrance subTaskEntrance;

	
	@Test
	void testBatchCreat() {
		String taskId = "TASK-001";
		List<String> urlList = Lists.newArrayList(
			"https://study-abroad.uchicago.edu/people/kylie-poulin-zahora",
			"https://ceas.uchicago.edu/korean-studies-funding",
			"https://college.uchicago.edu/navigating-college",
			"https://ceas.uchicago.edu/japanese-studies-funding",
			"https://ceas.uchicago.edu/chinese-studies-funding"
		);
		this.subTaskEntrance.batchCreat(taskId, urlList);
	}

	@Test
	void testReborn() {
		String subTaskId = "1936622110428987392";
		this.subTaskEntrance.reborn(subTaskId);
	}

}
