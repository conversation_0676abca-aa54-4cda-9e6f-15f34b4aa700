<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.college.mybatis.mapper.TitleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.college.mybatis.entity.Title">
        <id column="title" property="title" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        title
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.college.controller.vo.title.TitlePageVO">
		SELECT
			title
		FROM
			title AS t1
		<where>
        	1=1
	        <if test="title != null and title != ''">
	           	AND t1.title like concat('%', #{title}, '%')
            </if>
        </where>
    </select>

</mapper>
