<template>
  <div>
    <el-tabs v-model="activeTab" class="margin-top">
      <el-tab-pane label="Google参数" name="googleDebug">
        <sys-param-google-debug @configure-to-system-params="handleConfigureToSystemParams" />
      </el-tab-pane>
      <el-tab-pane label="爬虫参数" name="config">
        <el-form ref="form" :model="editForm" :rules="rules" label-width="200px">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>系统参数配置</span>
              <el-button type="primary" size="small" style="margin-left: 100px;" @click="checkParams"
                :loading="checkLoading" icon="el-icon-refresh">一键检测</el-button>
              <el-button type="success" size="small" style="margin-left: 10px;" @click="goBatchTaskCreate"
                :disabled="!allParamsValid" icon="el-icon-plus">创建任务</el-button>
            </div>
            <el-form-item label="谷歌搜索关键词" prop="googleKeyword">
              <div slot="label" style="display: inline-flex; align-items: center;">
                程序化搜索谷歌
                <!-- <el-tooltip content="打开谷歌" placement="top">
              <el-link :underline="false" href="https://www.google.com" target="_blank">
                <i class="el-icon-question" style="cursor: pointer; margin-left: 5px;"></i>
              </el-link>
            </el-tooltip> -->
              </div>
              <div class="form-item-container">
                <el-input type="textarea" v-model="editForm.googleKeyword" placeholder="程序化搜索谷歌" :rows="6"
                  class="half-width" show-word-limit></el-input>
                <div class="check-result" v-if="checkResults.googleKeyword">
                  <el-tag :type="checkResults.googleKeyword.valid ? 'success' : 'danger'">
                    {{ checkResults.googleKeyword.message }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="谷歌搜索引擎ID" prop="googleEngineID">
              <div slot="label" style="display: inline-flex; align-items: center;">
                谷歌搜索引擎ID
                <el-tooltip content="查看引擎配置" placement="top">
                  <el-link :underline="false" href="https://programmablesearchengine.google.com/controlpanel/all"
                    target="_blank">
                    <i class="el-icon-question" style="cursor: pointer; margin-left: 5px;"></i>
                  </el-link>
                </el-tooltip>
              </div>
              <div class="form-item-container">
                <el-input v-model="editForm.googleEngineID" placeholder="谷歌搜索引擎ID" class="half-width"
                  maxlength="64"></el-input>
                <div class="check-result" v-if="checkResults.googleEngineID">
                  <el-tag :type="checkResults.googleEngineID.valid ? 'success' : 'danger'">
                    {{ checkResults.googleEngineID.message }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="谷歌搜索引擎API密钥" prop="googleApiKey">
              <div slot="label" style="display: inline-flex; align-items: center;">
                谷歌搜索引擎API密钥
                <el-tooltip content="查看密钥" placement="top">
                  <el-link :underline="false" href="https://developers.google.com/custom-search/v1/introduction"
                    target="_blank">
                    <i class="el-icon-question" style="cursor: pointer; margin-left: 5px;"></i>
                  </el-link>
                </el-tooltip>
                <el-tooltip content="查看使用量" placement="top">
                  <el-link :underline="false"
                    href="https://console.cloud.google.com/apis/api/customsearch.googleapis.com/quotas" target="_blank">
                    <i class="el-icon-question" style="cursor: pointer; margin-left: 5px;"></i>
                  </el-link>
                </el-tooltip>
              </div>
              <div class="form-item-container">
                <el-input v-model="editForm.googleApiKey" placeholder="谷歌搜索引擎API密钥" class="half-width"
                  maxlength="64"></el-input>
                <div class="check-result" v-if="checkResults.googleApiKey">
                  <el-tag :type="checkResults.googleApiKey.valid ? 'success' : 'danger'">
                    {{ checkResults.googleApiKey.message }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="Markdown 接口超时 (秒)" prop="markdownTimeout">
              <div slot="label" style="display: inline-flex; align-items: center;">
                Markdown 接口超时 (秒)
                <el-tooltip content="查看爬虫服务器" placement="top">
                  <el-link :underline="false" :href="markdownServerUrl" target="_blank">
                    <i class="el-icon-question" style="cursor: pointer; margin-left: 5px;"></i>
                  </el-link>
                </el-tooltip>
              </div>
              <div class="form-item-container">
                <el-input-number v-model="editForm.markdownTimeout" placeholder="请输入Markdown接口超时" class="half-width"
                  :min="10" :max="300" style="width: 150px;"></el-input-number>
                <div class="check-result" v-if="checkResults.markdownTimeout">
                  <el-tag :type="checkResults.markdownTimeout.valid ? 'success' : 'danger'">
                    {{ checkResults.markdownTimeout.message }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
          </el-card>
          <el-card class="box-card">
            <el-form-item label="AI 模型" prop="aiModel">
              <div slot="label" style="display: inline-flex; align-items: center;">
                AI模型
                <el-tooltip content="查看模型广场" placement="top">
                  <el-link :underline="false" href="https://cloud.siliconflow.cn" target="_blank">
                    <i class="el-icon-question" style="cursor: pointer; margin-left: 5px;"></i>
                  </el-link>
                </el-tooltip>
              </div>
              <div class="form-item-container">
                <el-select v-model="editForm.aiModel" placeholder="请选择AI模型" class="half-width">
                  <el-option v-for="model in models" :key="model.name" :label="`[${model.price} 元] ${model.name}`"
                    :value="model.name"></el-option>
                </el-select>
                <div class="check-result" v-if="checkResults.aiModel">
                  <el-tag :type="checkResults.aiModel.valid ? 'success' : 'danger'">
                    {{ checkResults.aiModel.message }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="AI 密钥" prop="aiApiKey">
              <div class="form-item-container">
                <el-input v-model="editForm.aiApiKey" placeholder="请输入AI密钥" class="half-width"
                  maxlength="64"></el-input>
                <div class="check-result" v-if="checkResults.aiApiKey">
                  <el-tag :type="checkResults.aiApiKey.valid ? 'success' : 'danger'">
                    {{ checkResults.aiApiKey.message }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="AI 接口超时 (秒)" prop="aiTimeout">
              <div class="form-item-container">
                <el-input-number v-model="editForm.aiTimeout" placeholder="请输入AI密钥" class="half-width" :min="10"
                  :max="300" style="width: 150px;"></el-input-number>
                <div class="check-result" v-if="checkResults.aiTimeout">
                  <el-tag :type="checkResults.aiTimeout.valid ? 'success' : 'danger'">
                    {{ checkResults.aiTimeout.message }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
            <!-- <el-form-item label="AI 系统提示词" prop="aiSystemPrompt">
          <div class="form-item-container">
            <el-input type="textarea" v-model="editForm.aiSystemPrompt" placeholder="请输入系统提示词" :rows="2" class="half-width" show-word-limit></el-input>
            <div class="check-result" v-if="checkResults.aiSystemPrompt">
              <el-tag :type="checkResults.aiSystemPrompt.valid ? 'success' : 'danger'">
                {{ checkResults.aiSystemPrompt.message }}
              </el-tag>
            </div>
          </div>
        </el-form-item> -->
            <el-form-item label="AI 用户提示词" prop="aiUserPrompt">
              <div class="form-item-container">
                <el-input type="textarea" v-model="editForm.aiUserPrompt" placeholder="请输入deepseek用户提示词" :rows="7"
                  class="half-width" show-word-limit></el-input>
                <div class="check-result" v-if="checkResults.aiUserPrompt">
                  <el-tag :type="checkResults.aiUserPrompt.valid ? 'success' : 'danger'">
                    {{ checkResults.aiUserPrompt.message }}
                  </el-tag>
                </div>
              </div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitForm">保存</el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-card>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { sysParamUpdate, sysParamGet, sysParamCheck, sysParamMarkdownServerUrl } from '../../api/sysParam'
import { loadToken } from '../../utils/util'
import SysParamGoogleDebug from './sysParamGoogleDebug.vue'
export default {
  data() {
    return {
      loading: false,
      checkLoading: false,
      markdownServerUrl: '', // 爬虫服务器地址
      activeTab: 'googleDebug', // 当前激活的tab
      editForm: {
        id: 1,
        aiSystemPrompt: '',
        aiUserPrompt: '',
        aiModel: null,
        aiApiKey: '',
        aiTimeout: null,
        markdownTimeout: null,
        googleKeyword: '',
        googleEngineID: '',
        googleApiKey: '',
        token: loadToken(),
      },
      models: [
        { "name": "deepseek-ai/DeepSeek-R1-0528-Qwen-3-8B", "price": 0 },
        { "name": "Qwen/Qwen3-8B", "price": 0 },
        { "name": "THUDM/GLM-Z1-9B-0414", "price": 0 },
        { "name": "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B", "price": 0 },
        { "name": "Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B", "price": 0.35 },
        { "name": "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B", "price": 0.7 },
        { "name": "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B", "price": 1.26 },
        { "name": "Qwen/Qwen3-14B", "price": 2 },
        { "name": "Qwen/Qwen3-30B-A3B", "price": 2.8 },
        { "name": "Tongyi-Zhiwen/QwenLong-L1-32B", "price": 4 },
        { "name": "Qwen/Qwen3-32B", "price": 4 },
        { "name": "THUDM/GLM-Z1-32B-0414", "price": 4 },
        { "name": "Qwen/QwQ-32B", "price": 4 },
        { "name": "THUDM/GLM-Z1-Rumination-32B-0414", "price": 4 },
        { "name": "Qwen/Qwen3-235B-A22B", "price": 10 },
        { "name": "deepseek-ai/DeepSeek-R1", "price": 16 },
        { "name": "MiniMaxAI/MiniMax-M1-80k", "price": 16 }
      ],
      checkResults: {}, // 检测结果
      userEntity: undefined,
      rules: {
        //aiSystemPrompt: [
        //  { required: true, message: '必填', trigger: 'blur' },
        //],
        aiUserPrompt: [
          { required: true, message: '必填', trigger: 'blur' },
        ],
        aiModel: [
          { required: true, message: '必填', trigger: 'change' },
        ],
        aiApiKey: [
          { required: true, message: '必填', trigger: 'blur' },
        ],
        aiTimeout: [
          { required: true, message: '必填', trigger: 'blur' },
        ],
        markdownTimeout: [
          { required: true, message: '必填', trigger: 'blur' },
        ],
        googleKeyword: [
          { required: true, message: '必填', trigger: 'blur' },
        ],
        googleEngineID: [
          { required: true, message: '必填', trigger: 'blur' },
        ],
        googleApiKey: [
          { required: true, message: '必填', trigger: 'blur' },
        ]
      },
    }
  },
  components: {
    SysParamGoogleDebug
  },

  created() {
    // 加载用户缓存
    this.userEntity = JSON.parse(localStorage.getItem('userEntity'));
    // 加载系统参数配置
    this.getSysParamConfig();
    // 获取爬虫服务器地址
    this.getMarkdownServerUrl();
  },
  methods: {
    // 获取爬虫服务器地址
    getMarkdownServerUrl() {
      sysParamMarkdownServerUrl({}).then(res => {
        if (res.success) {
          this.markdownServerUrl = res.data + "/docs";
        } else {
          this.$message.error(res.message);
        }
      }).catch(err => {
        this.$message.error('获取爬虫服务器地址失败');
      });
    },
    // 获取系统参数配置
    getSysParamConfig() {
      this.loading = true;
      sysParamGet({}).then(res => {
        this.loading = false;
        if (res.success) {
          this.editForm = {
            ...res.data,
            token: loadToken()
          };
        } else {
          this.$message.error(res.message);
        }
      }).catch(err => {
        this.loading = false;
        this.$message.error('获取系统参数配置失败');
      });
    },
    // 提交表单
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          sysParamUpdate(this.editForm).then(res => {
            this.loading = false;
            if (res.success) {
              this.$message.success('保存成功');
              this.checkParams();
            } else {
              this.$message.error(res.message || '保存失败');
            }
          }).catch(err => {
            this.loading = false;
            this.$message.error('保存失败');
          });
        } else {
          return false;
        }
      });
    },
    // 重置表单
    resetForm() {
      this.$refs.form.resetFields();
      this.getSysParamConfig();
    },
    // 检测系统参数
    checkParams() {
      this.checkLoading = true;

      // 重置表单
      this.resetForm();

      // 正常应该使用实际的接口调用
      sysParamCheck(this.editForm).then(res => {
        this.checkLoading = false;
        if (res.success) {
          this.checkResults = res.data;
        } else {
          this.$message.error(res.message);
        }
      }).catch(err => {
        this.checkLoading = false;
        this.$message.error('检测失败');
      });

    },
    // 批量创建任务
    goBatchTaskCreate() {
      this.$router.push('/batchTaskCreate');
    },

    // 处理从调试组件传来的配置数据
    handleConfigureToSystemParams(data) {
      // 将数据复制到系统参数配置表单
      this.editForm.googleKeyword = data.googleKeyword;
      this.editForm.aiUserPrompt = data.aiUserPrompt;

      // 切换到系统参数配置tab
      this.activeTab = 'config';

      // 延迟执行保存，确保tab切换完成
      this.$nextTick(() => {
        this.submitForm();
      });

      this.$message.success('已配置到系统参数并保存');
    },

  },
  computed: {
    // 判断所有系统参数是否全部有效
    allParamsValid() {
      // 如果没有检测结果，表示未进行检测，默认禁用
      if (Object.keys(this.checkResults).length === 0) {
        return false;
      }

      // 判断所有参数是否都有效
      for (const key in this.checkResults) {
        // 检查对象是否为null或undefined
        if (!this.checkResults[key]) {
          continue;
        } else if (!this.checkResults[key].valid) {
          return false;
        }
      }

      return true;
    }
  }
}
</script>

<style scoped>
.margin-top {
  margin-top: 0px;
  padding: 0px !important;
}

.el-form-item__error {
  color: #F56C6C;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  left: 0;
  top: 100%;
}

::v-deep .el-form-item__label {
  width: 200px !important;
}

.half-width {
  width: 40%;
}

.form-item-container {
  display: flex;
  align-items: center;
}

.check-result {
  margin-left: 20px;
}

.el-card__body,
.el-main {
    padding: 0px!important ;
}
</style>
