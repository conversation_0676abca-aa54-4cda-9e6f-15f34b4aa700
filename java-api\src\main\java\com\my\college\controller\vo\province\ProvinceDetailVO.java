package com.my.college.controller.vo.province;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 查看实体详情_美国50个州州名
 *
 * <AUTHOR>
 * @date 2025-05-25
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class ProvinceDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字母缩写
     */
    private String provinceId;

    /**
     * 英文名称
     */
    private String englishName;

    /**
     * 中文名称
     */
    private String chineseName;

}
