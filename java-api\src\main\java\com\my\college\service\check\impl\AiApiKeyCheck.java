package com.my.college.service.check.impl;

import java.lang.reflect.Field;
import java.math.BigDecimal;

import org.apache.commons.lang3.StringUtils;

import com.my.college.forest.siliconflow.SiliconflowClient;
import com.my.college.forest.siliconflow.onerrror.SiliconflowOnError;
import com.my.college.service.check.AbstractCheck;
import com.my.college.service.check.result.CheckResult;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;

/**
 * AI密钥检测器
 * <AUTHOR>
 */
public class AiApiKeyCheck extends AbstractCheck {


	@Override
    protected CheckResult doCheck(Object fieldValue, Field field, Object obj) {
        String apiKey = (String) fieldValue;
        
        if (StringUtils.isBlank(apiKey)) {
            return fail(this.fieldLabel + "不能为空");
        }
        
        SiliconflowOnError onErr = new SiliconflowOnError();
		BigDecimal balance = SpringUtil.getBean(SiliconflowClient.class).getBalance(apiKey, onErr);
        if (onErr.isError()) {
        	return fail(this.fieldLabel + onErr.getErrMsg());
        } else {
        	String strBalance = StrUtil.format("有效。 余额: {} 元", balance);
        	return success(strBalance);
        }
    }
    
}
