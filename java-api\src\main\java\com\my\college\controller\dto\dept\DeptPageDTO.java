package com.my.college.controller.dto.dept;

import java.io.Serializable;

import com.alibaba.fastjson2.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.college.controller.vo.dept.DeptPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_部门表
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class DeptPageDTO 
						extends PageDTO<DeptPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门
     */
    private String dept;

}
