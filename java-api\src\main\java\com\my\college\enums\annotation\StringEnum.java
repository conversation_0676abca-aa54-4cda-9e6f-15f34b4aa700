package com.my.college.enums.annotation;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.util.Assert;

import com.my.college.exception.BusinessException;

/**
 * 字符串作为key的枚举
 * <AUTHOR>
 * @date 2019年7月29日
 */
public interface StringEnum {

    String name();

    /**
     * 标签
     * @return
     */
    String getLabel();
    
    
    /**	使用label创建对象 */
    @SuppressWarnings("unchecked")
	static <T> T labelOf(Class<T> cls, String label) {
    	Assert.notNull(label, "label不能为空");
        Method method;
		try {
			// 入参是否为子类
			if (!StringEnum.class.isAssignableFrom(cls)) {
				BusinessException.by(String.format("%s不是StringEnum的子类，无法判断", cls.getSimpleName()));
			}
			method = cls.getMethod("values");
			StringEnum[] enums = (StringEnum[]) method.invoke(null);
			for (StringEnum tEnum : enums) {
				if (tEnum.getLabel().equals(label)) {
					return (T) tEnum;
				}
			}
			List<String> labels = Arrays.asList(enums).stream().map(e -> e.getLabel()).collect(Collectors.toList());
			String errMsg = String.format("枚举【%s】不包含此label:%s! 仅包含:%s", cls.getSimpleName(), label, labels);
			BusinessException.by(errMsg);
		} catch (BusinessException e) {
			throw e;
		} catch (Exception e) {
			e.printStackTrace();
		}
        
        return null;
	}

    /**
     * 名称
     * @return
     */
    default String getName() {
    	return this.name();
    }
}
