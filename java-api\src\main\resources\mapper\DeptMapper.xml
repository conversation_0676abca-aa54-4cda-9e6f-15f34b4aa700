<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.college.mybatis.mapper.DeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.college.mybatis.entity.Dept">
        <id column="dept" property="dept" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        dept
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.college.controller.vo.dept.DeptPageVO">
		SELECT
			dept
		FROM
			dept AS t1
		<where>
        	1=1
	        <if test="dept != null and dept != ''">
	           	AND t1.dept like concat('%', #{dept}, '%')
            </if>
        </where>
    </select>

</mapper>
