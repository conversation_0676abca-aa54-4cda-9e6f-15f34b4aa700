package com.my.college.controller.dto.sys_param;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import com.my.college.service.check.annotation.Check;
import com.my.college.service.check.impl.AiTimeoutCheck;
import com.my.college.service.check.impl.GoogleApiKeyCheck;
import com.my.college.service.check.impl.GoogleEngineIDCheck;
import com.my.college.service.check.impl.GoogleKeywordCheck;
import com.my.college.service.check.impl.MarkdownTimeoutCheck;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_系统参数配置表
 *
 * <AUTHOR>
 * @date 2025-05-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysParamUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * AI模型
     */
    private String aiModel;

    /**
     * AI系统提示词
     */
	private String aiSystemPrompt;
    
    /**
     * AI用户提示词
     */
	private String aiUserPrompt;

    /**
     * AI接口秘钥
     */
	private String aiApiKey;

    /**
     * 谷歌搜索引擎API密钥
     */
	@NotBlank
    private String googleApiKey;
    
    /**
     * 谷歌搜索引擎ID
     */
	@NotBlank
    private String googleEngineID;
    
    /**
     * 谷歌搜索关键词
     */
	@NotBlank
    private String googleKeyword;
	
    /**
     * AI接口超时
     */
	@NotBlank
    private String aiTimeout;
    
    /**
     * markdown超时
     */
	@NotBlank
    private String markdownTimeout;
}
