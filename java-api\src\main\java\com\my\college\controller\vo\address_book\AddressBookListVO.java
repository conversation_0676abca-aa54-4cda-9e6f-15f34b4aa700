package com.my.college.controller.vo.address_book;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 通讯录列表VO
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class AddressBookListVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 学校ID
     */
    private Integer schoolId;

    /**
     * 学校中文名称
     */
    private String schoolChineseName;

    /**
     * 部门
     */
    private String department;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 职位
     */
    private String position;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否手动添加(0-否,1-是)
     */
    private Boolean manualFlag;

} 