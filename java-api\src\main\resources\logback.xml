<?xml version="1.0" encoding="UTF-8" ?>
<configuration>

	<!--定义日志文件的存储地址 -->    
    <property name="LOG_HOME" value="logs/" />    
    
    <!-- %m输出的信息,%p日志级别,%t线程名,%d日期,%c类的全名,%i索引【从数字0开始递增】,,, -->
    <!-- appender是configuration的子节点，是负责写日志的组件。 -->
    <!-- ConsoleAppender：把日志输出到控制台 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d %p [%thread] (%file:%line\)- %m%n</pattern>
            <!-- 控制台也要使用UTF-8，不要使用GBK，否则会中文乱码 -->
            <charset>UTF-8</charset>
        </encoder>
    </appender>


	<!-- filter说明 
	onMatch="ACCEPT" 表示匹配该级别及以上
	onMatch="DENY" 表示不匹配该级别及以上
	onMatch="NEUTRAL" 表示该级别及以上的，由下一个filter处理，如果当前是最后一个，则表示匹配该级别及以上
	onMismatch="ACCEPT" 表示匹配该级别以下
	onMismatch="NEUTRAL" 表示该级别及以下的，由下一个filter处理，如果当前是最后一个，则不匹配该级别以下的
	onMismatch="DENY" 表示不匹配该级别以下的
	 -->
	<!-- 时间滚动输出 level为 info 日志 -->
	<appender name="file-info" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_HOME}/info.log</file>
		
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>info</level>
		</filter>

		<!-- 每天产生一个日志文件，最多30个，自动回滚 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_HOME}/info.%d{yyyy-MM-dd}.log</fileNamePattern>
			<maxHistory>30</maxHistory>
		</rollingPolicy>

		<!-- 在日志文件超过5MB时进行归档，并且归档文件后缀只要是.zip或.gz就会自动压缩日志归档 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
			<fileNamePattern>${LOG_HOME}/info.%i.log.zip</fileNamePattern>
			<minIndex>1</minIndex>
			<maxIndex>10</maxIndex>
		</rollingPolicy>

		<!-- 日志文件最大的大小 -->
		<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<maxFileSize>50MB</maxFileSize>
		</triggeringPolicy>

		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %p [%5thread] %logger{40}:%line - %msg%n</pattern>
		</encoder>
	</appender>
	
	<!-- 时间滚动输出 level为 ERROR 日志 -->
	<appender name="file-error" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_HOME}/error.log</file>
	
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY </onMismatch>
		</filter>
		
		<!-- 每天产生一个日志文件，最多30个，自动回滚 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_HOME}/error.%d{yyyy-MM-dd}.log</fileNamePattern>
			<maxHistory>30</maxHistory>
		</rollingPolicy>
		
		<!-- 在日志文件超过5MB时进行归档，并且归档文件后缀只要是.zip或.gz就会自动压缩日志归档 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
			<fileNamePattern>${LOG_HOME}/error.%i.log.zip</fileNamePattern>
			<minIndex>1</minIndex>
			<maxIndex>10</maxIndex>
		</rollingPolicy>

		<!-- 日志文件最大的大小 -->
		<triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
			<maxFileSize>50MB</maxFileSize>
		</triggeringPolicy>
		
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%5thread] %logger{40}:%line - %msg%n</pattern>
		</encoder>
	</appender>
	
	<!-- 只显示info级别以上的 -->
	<root level="INFO">  
        <appender-ref ref="STDOUT" />  
        <appender-ref ref="file-info" />
        <appender-ref ref="file-error" />
    </root> 
       
	<!-- 第三方包的日志过滤为warn，用于避免刷屏 -->
	<logger name="net.sf.ehcache" level="WARN" />
	<logger name="org.jboss.logging" level="WARN" />
	<logger name="com.alibaba" level="WARN" />
    <logger name="org.hibernate" level="WARN" />
    <logger name="org.springframework" level="WARN" />  
    <logger name="org.apache" level="WARN" />
    <logger name="org.thymeleaf.TemplateEngine" level="WARN" />
    <logger name="org.thymeleaf.TemplateEngine.CONFIG" level="WARN" />
    <logger name="com.zaxxer.hikari" level="WARN" />
    <logger name="springfox.documentation" level="WARN" />
    <logger name="org.quartz.core.QuartzSchedulerThread" level="WARN" />
    <logger name="org.quartz.impl.jdbcjobstore.StdRowLockSemaphore" level="WARN" />
    <logger name="org.quartz.core.JobRunShell" level="WARN" />
    <logger name="org.springframework.http.client.support.HttpAccessor" level="WARN" />
    <logger name="org.springframework.web.client.RestTemplate" level="WARN" />
    <logger name="org.apache.http.client.protocol.ResponseProcessCookies" level="ERROR" />
    
    <!-- spring的只输出到控制台和error -->  
    <logger name="org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver" level="ERROR" >  
        <appender-ref ref="STDOUT" />  
        <appender-ref ref="file-error" />
    </logger>


</configuration>