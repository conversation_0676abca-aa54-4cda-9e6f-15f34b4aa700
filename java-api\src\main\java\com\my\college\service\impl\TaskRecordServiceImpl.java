package com.my.college.service.impl;

import java.time.LocalDateTime;

import javax.annotation.PostConstruct;

import org.springframework.boot.logging.LogLevel;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.college.controller.dto.task_record.TaskRecordPageDTO;
import com.my.college.controller.vo.task_record.TaskRecordDetailVO;
import com.my.college.controller.vo.task_record.TaskRecordPageVO;
import com.my.college.enums.TaskStatus;
import com.my.college.mybatis.entity.TaskRecord;
import com.my.college.mybatis.mapper.TaskRecordMapper;
import com.my.college.service.TaskLogService;
import com.my.college.service.TaskRecordService;
import com.my.college.task.TaskContext;
import com.my.college.util.ColumnLambda;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * 任务记录表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-05-03
 */
@Service
@RequiredArgsConstructor
public class TaskRecordServiceImpl extends ServiceImpl<TaskRecordMapper, TaskRecord> implements TaskRecordService {

	private final TaskLogService taskLogService;

	
	// 启动时，将进行中的改为中断
	@PostConstruct
	public void init() {
		LambdaUpdateWrapper<TaskRecord> wrapper = new LambdaUpdateWrapper<TaskRecord>()
				.set(TaskRecord::getStatus, TaskStatus.STOP.name())
				.eq(TaskRecord::getStatus, TaskStatus.RUN.name());
		this.update(wrapper);
	}
	
	@Override
	public TaskRecordDetailVO detail(String id) {
		TaskRecord entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, TaskRecordDetailVO.class);
	}

	@Override
	public Page<TaskRecordPageVO> page(TaskRecordPageDTO pageDTO) {
		// 默认按taskId倒序排序
		if (CollectionUtil.isEmpty(pageDTO.getOrders())) {
			pageDTO.addOrder(OrderItem.desc(new ColumnLambda<TaskRecord>().columnsToString(TaskRecord::getTaskId)));
		}
		
		return this.baseMapper.page(pageDTO);
	}

	@Override
	public TaskRecord insert() {
		TaskRecord taskRecord = new TaskRecord();
		taskRecord.setBatchNumber(TaskContext.getBatchNumber());
		taskRecord.setTaskId(TaskContext.getTaskId());
		taskRecord.setSchoolId(TaskContext.getSchoolId());
		taskRecord.setStartTime(LocalDateTime.now());
		taskRecord.setStatus(TaskStatus.RUN.name());
		this.save(taskRecord);
		
		// 记日志
		String content = "任务创建成功";
		this.taskLogService.save(LogLevel.INFO, content);
		return taskRecord;
	}

	@Override
	public void fail(String failRemark) {
		TaskRecord entity = new TaskRecord();
		entity.setTaskId(TaskContext.getTaskId());
		entity.setStatus(TaskStatus.FAIL.name());
		entity.setEndTime(LocalDateTime.now());
		entity.setFailRemark(failRemark);
		this.updateById(entity);
		
		// 记日志
		String content = StrUtil.format("{}", failRemark);
		this.taskLogService.save(LogLevel.ERROR, content);
	}
	
	@Override
	public TaskRecord reborn(String taskId) {
		TaskRecord dto = new TaskRecord();
		dto.setTaskId(taskId);
		dto.setStatus(TaskStatus.REBORN.name());
		this.updateById(dto);

		// 记日志
		TaskRecord entity = this.getById(taskId);
		TaskContext.set(entity.getBatchNumber(), taskId, entity.getSchoolId());
		this.taskLogService.save(LogLevel.INFO, "任务已重试");
		return entity;
	}
	
	@Override
	public void updateRequest(String request) {
		// 更新任务的请求参数
		TaskRecord entity = new TaskRecord();
		entity.setTaskId(TaskContext.getTaskId());
		entity.setRequest(request);
		this.updateById(entity);
		
		// 记日志
		this.taskLogService.save(LogLevel.INFO, "请求参数准备完毕");
	}
	
	@Override
	public void updateResponse(String response) {
		LocalDateTime now = LocalDateTime.now();
		TaskRecord entity = new TaskRecord();
		entity.setTaskId(TaskContext.getTaskId());
		entity.setResponse(response);
		entity.setEndTime(now);
		this.updateById(entity);

		// 记日志
		this.taskLogService.save(LogLevel.INFO, "响应成功");
	}
	
	@Override
	public void succeed() {
		LocalDateTime now = LocalDateTime.now();
		TaskRecord entity = new TaskRecord();
		entity.setTaskId(TaskContext.getTaskId());
		entity.setStatus(TaskStatus.SUCCESS.name());
		entity.setFinishTime(now);
		this.updateById(entity);

		// 记日志
		this.taskLogService.save(LogLevel.INFO, "主任务处理完毕");
	}
	
	@Override
	public void succeedWhenSubtaskSucceed(String taskId) {
	    boolean bool = this.baseMapper.succeedWhenSubtaskSucceed(taskId);
	    if (bool) {
	    	// 设置 TaskContext 以便 taskLogService 能获取到值
	    	TaskRecord taskRecord = this.getById(taskId);
	    	TaskContext.set(taskRecord.getBatchNumber(), taskId, taskRecord.getSchoolId());
	    	this.taskLogService.save(LogLevel.INFO, "主任务处理完毕");
	    	// 清理上下文
	    	TaskContext.remove();
	    } else {
	    	System.out.println("主任务还没搞完哟~~~~");
	    }
	    
	}
	
}
