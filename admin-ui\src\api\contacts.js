import { reqGet, reqPut, reqDelete, reqPost } from './axiosFun'
import qs from 'qs'

// 分页查询联系人
export function contactsPage(parameter) {
  return reqPost('/contacts/page', parameter)
}

// 查看联系人详情
export function contactsDetail(parameter) {
  return reqPost('/contacts/detail', parameter)
}

// Mock数据 - 用于开发测试
export function getMockContactsData() {
  return new Promise((resolve) => {
    // 模拟后端数据
    const mockData = {
      "code": 200,
      "message": "操作成功",
      "data": {
        "records": [
          {
            "id": "1001",
            "name": "朱小明",
            "email": "<EMAIL>",
            "department": "中央空调学院",
            "position": "院长",
            "phone": "13912345678",
            "createTime": "2024-04-01 08:00:00"
          },
          {
            "id": "1002",
            "name": "李华",
            "email": "<EMAIL>",
            "department": "冷冻工程系",
            "position": "系主任",
            "phone": "13823456789",
            "createTime": "2024-04-02 09:15:00"
          },
          {
            "id": "1003",
            "name": "王强",
            "email": "<EMAIL>",
            "department": "空调维修部",
            "position": "部门经理",
            "phone": "13734567890",
            "createTime": "2024-04-03 10:30:00"
          },
          {
            "id": "1004",
            "name": "张敏",
            "email": "<EMAIL>",
            "department": "智能控制研究所",
            "position": "研究员",
            "phone": "13645678901",
            "createTime": "2024-04-04 11:45:00"
          },
          {
            "id": "1005",
            "name": "陈晓",
            "email": "<EMAIL>",
            "department": "暖通设计中心",
            "position": "设计师",
            "phone": "13556789012",
            "createTime": "2024-04-05 13:00:00"
          },
          {
            "id": "1006",
            "name": "刘伟",
            "email": "<EMAIL>",
            "department": "节能技术部",
            "position": "技术主管",
            "phone": "13467890123",
            "createTime": "2024-04-06 14:15:00"
          },
          {
            "id": "1007",
            "name": "赵琳",
            "email": "<EMAIL>",
            "department": "客户服务中心",
            "position": "客服经理",
            "phone": "13378901234",
            "createTime": "2024-04-07 15:30:00"
          },
          {
            "id": "1008",
            "name": "孙明",
            "email": "<EMAIL>",
            "department": "质量控制部",
            "position": "质检员",
            "phone": "13289012345",
            "createTime": "2024-04-08 16:45:00"
          },
          {
            "id": "1009",
            "name": "周霞",
            "email": "<EMAIL>",
            "department": "市场推广部",
            "position": "市场专员",
            "phone": "13190123456",
            "createTime": "2024-04-09 09:00:00"
          },
          {
            "id": "1010",
            "name": "吴刚",
            "email": "<EMAIL>",
            "department": "人力资源部",
            "position": "HR主管",
            "phone": "13001234567",
            "createTime": "2024-04-10 10:15:00"
          }
        ],
        "total": 10,
        "size": 10,
        "current": 1,
        "orders": [],
        "searchCount": true,
        "pages": 1
      }
    };

    setTimeout(() => {
      resolve(mockData);
    }, 500);
  });
}
