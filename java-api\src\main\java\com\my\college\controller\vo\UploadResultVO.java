package com.my.college.controller.vo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class UploadResultVO {

	/**
	 * 异常信息
	 */
	List<String> errList;
	
	/**
	 * 有效记录数
	 */
	Integer validCnt;
	
	/**
	 * 总记录数
	 */
	Integer totalCnt;
	
	/**
	 * 无效记录数
	 */
	Integer invalidCnt;
	
	/**
	 * 是否全部有效
	 */
	Boolean valid;
}

