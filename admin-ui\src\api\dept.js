import { reqGet, reqPut, reqDelete, reqPost } from './axiosFun';

// 分页
export const deptPage = (params) => { return reqGet("/dept/page", params) };

// 获取全部部门列表
export const deptList = (params) => { return reqGet("/dept/list", params) };

// 获取详情
export const deptDetail = (params) => { return reqGet("/dept/" + params.dept) };

// 创建部门
export const createDept = (params) => {return reqPost("/dept", params);};

// 更新部门
export const updateDept = (params) => {return reqPut("/dept", params);};

// 删除部门
export const deleteDept = (params) => { return reqDelete("/dept", params) };
