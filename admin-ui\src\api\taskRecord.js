import { reqGet, reqPut, reqDelete, reqPost, reqUpload } from './axiosFun';

// 分页
export const taskRecordPage = (params) => { return reqGet("/task-record/page", params) };

// 获取详情
export const taskRecordDetail = (params) => { return reqGet("/task-record/" + params.taskId) };

// 批量创建任务
export const createBatchTask = (params) => { return reqPost("/task-record/create-batch-task", params) };

// 单次任务
export const createSingleTask = (params) => { return reqPost("/task-record/create-single-task", params) };

// 获取任务日志
export const taskRecordLogs = (params) => { return reqGet("/task-record/logs", params) };

// 获取子任务日志
export const subTaskRecordLogs = (params) => { return reqGet("/sub-task-record/logs", params) };

// 重试任务
export const taskRecordReborn = (params) => { return reqPost("/task-record/reborn", params) };
