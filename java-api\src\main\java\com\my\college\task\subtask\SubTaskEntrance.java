package com.my.college.task.subtask;

import java.util.List;

import org.springframework.stereotype.Component;

import com.my.college.controller.vo.sys_param.SysParamVO;
import com.my.college.mybatis.entity.SubTaskRecord;
import com.my.college.service.SubTaskRecordService;
import com.my.college.service.SysParamService;

import lombok.RequiredArgsConstructor;

/**
 * 子任务入口
 * <AUTHOR>
 *
 */
@Component
@RequiredArgsConstructor
public class SubTaskEntrance {

	private final SysParamService sysParamService;
	private final SubTaskProcess subTaskProcess;
	private final SubTaskRecordService subTaskRecordService;
	
	
	/**
	 * 创建批量子任务
	 */
	public void batchCreat(String taskId, List<String> urlList) {
		SysParamVO sysParam = this.sysParamService.get();
		for (String url : urlList) {
			this.subTaskProcess.process(taskId, url, sysParam);
		}
	}

	/**
	 * 子任务重生
	 * @param subTaskId
	 */
	public void reborn(String subTaskId) {
		SysParamVO sysParam = this.sysParamService.get();
		// 子任务状态:已重试
		SubTaskRecord subTaskRecord = this.subTaskRecordService.reborn(subTaskId);
		// 新子任务（旧主任务号）
		String taskId = subTaskRecord.getTaskId();
		String url = subTaskRecord.getUrl();
		this.subTaskProcess.process(taskId, url, sysParam);
	}
	
}
