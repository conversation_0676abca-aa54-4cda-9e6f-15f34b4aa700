<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.college.mybatis.mapper.TaskRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.college.mybatis.entity.TaskRecord">
        <id column="task_id" property="taskId" />
        <result column="batch_number" property="batchNumber" />
        <result column="school_id" property="schoolId" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="finish_time" property="finishTime" />
        <result column="status" property="status" />
        <result column="fail_remark" property="failRemark" />
        <result column="request" property="request" />
        <result column="response" property="response" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        task_id, batch_number, school_id, start_time, end_time, finish_time, status, fail_remark, 
			request, response, create_time, update_time
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.college.controller.vo.task_record.TaskRecordPageVO">
		SELECT
			task_id, batch_number, school_id, start_time, end_time, finish_time, status, fail_remark, 
			request, response, create_time, update_time
		FROM
			task_record AS t1
		<where>
        	1=1
	        <if test="taskId != null and taskId != ''">
	           	AND t1.task_id = #{taskId}
            </if>
	        <if test="batchNumber != null">
	           	AND t1.batch_number = #{batchNumber}
            </if>            
	        <if test="schoolId != null">
	           	AND t1.school_id = #{schoolId}
            </if>
	        <if test="startTime != null">
	           	AND t1.start_time &gt;= #{startTime}
            </if>
	        <if test="endTime != null">
	           	AND t1.end_time &lt;= #{endTime}
            </if>
	        <if test="finishTime != null and finishTime != ''">
	           	AND t1.finish_time = #{finishTime}
            </if>
	        <if test="status != null and status != ''">
	           	AND t1.status = #{status}
            </if>
	        <if test="createTime != null and createTime != ''">
	           	AND t1.create_time = #{createTime}
            </if>
	        <if test="updateTime != null and updateTime != ''">
	           	AND t1.update_time = #{updateTime}
            </if>
        </where>
    </select>
    
    <!-- 
    	succeedWhenSubtaskSucceed
	    如果所有子任务的状态全部都是SUCCESS或REBORN， 则主任务改为SUCCESS
     -->
    <update id="succeedWhenSubtaskSucceed">
    	UPDATE task_record
		SET status = 'SUCCESS',
		    update_time = NOW(),
		    finish_time = NOW()
		WHERE 
			task_id = #{taskId}
			AND NOT EXISTS (
			    SELECT 1 FROM sub_task_record
			    WHERE task_id = task_record.task_id
			      AND status NOT IN ('SUCCESS', 'REBORN')
			)
    </update>

</mapper>
