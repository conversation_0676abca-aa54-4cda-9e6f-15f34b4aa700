<template>
  <div>
    <!-- breadcrumb -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">学校管理</el-breadcrumb-item>
      <el-breadcrumb-item>学校</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- search -->
    <el-form :inline="true" :model="formInline" class="user-search" ref="searchForm">
      <el-form-item label="查询：">
        <el-select size="small" v-model="formInline.provinceId" placeholder="选择省份" clearable filterable style="width: 120px;">
          <el-option
            v-for="item in provinceList"
            :key="item.provinceId"
            :label="`${item.provinceId} - ${item.chineseName}`"
            :value="item.provinceId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input size="small" v-model="formInline.chineseName" placeholder="中文名称" clearable style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input size="small" v-model="formInline.englishName" placeholder="英文名称" clearable style="width: 130px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input size="small" v-model="formInline.website" placeholder="学校网址" clearable style="width: 130px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" icon="el-icon-search" @click="search">查询</el-button>
        <el-button size="small" type="primary" icon="el-icon-refresh" @click="resetSearch()">重置</el-button>
        <el-button size="small" type="primary" icon="el-icon-plus" @click="handleAdd" v-if="userEntity && userEntity.adminFlag">新增</el-button>
        <el-button size="small" type="danger" icon="el-icon-delete" @click="handleBatchDelete" v-if="userEntity && userEntity.adminFlag && multipleSelection.length > 0">删除</el-button>
        <el-button size="small" type="success" icon="el-icon-upload2" @click="handleImport" v-if="userEntity && userEntity.adminFlag">导入</el-button>
        <el-button size="small" type="warning" icon="el-icon-download" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <!--list-->
    <el-table size="small" ref="ids" @sort-change="getSortPageData" @selection-change="handleSelectionChange" :data="listData" highlight-current-row v-loading="loading" border element-loading-text="loading" style="width: 100%;">
      <el-table-column type="selection" width="39" v-if="userEntity && userEntity.adminFlag"></el-table-column>
      <el-table-column prop="schoolId" label="编号" width="50" show-overflow-tooltip></el-table-column>
      <el-table-column prop="provinceId" label="省份" width="70" sortable="custom">
        <template slot-scope="scope">
          <el-tooltip :content="getProvinceName(scope.row.provinceId)" placement="top">
            <span>{{ scope.row.provinceId }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="chineseName" label="中文名称" width="160" show-overflow-tooltip sortable="custom"></el-table-column>
      <el-table-column prop="englishName" label="英文名称" width="220" show-overflow-tooltip sortable="custom"></el-table-column>
      <el-table-column prop="website" label="学校网址" width="240" show-overflow-tooltip sortable="custom">
        <template slot-scope="scope">
          <el-link type="primary" :href="scope.row.website" target="_blank">{{ scope.row.website }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" width="150" show-overflow-tooltip sortable="custom"></el-table-column>
      <el-table-column prop="updateTime" label="更新时间" width="150" show-overflow-tooltip sortable="custom"></el-table-column>
      <el-table-column align="left" label="操作" min-width="140">
        <template slot-scope="scope">
          <el-link>
            <i class="el-icon-view" @click="handleView(scope.row)"></i>
          </el-link>
          <el-link v-if="userEntity && userEntity.adminFlag">
            <i class="el-icon-edit" @click="handleEdit(scope.row)"></i>
          </el-link>
          <el-link v-if="userEntity && userEntity.adminFlag">
            <i class="el-icon-delete" @click="handleDelete(scope.row)"></i>
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <!-- page -->
    <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>

    <!-- 子组件 -->
    <schoolInsert @callback="getPageData" ref="schoolInsert"></schoolInsert>
    <schoolUpdate @callback="getPageData" ref="schoolUpdate"></schoolUpdate>
    <schoolDetail ref="schoolDetail"></schoolDetail>
    <schoolUpload @refreshData="getPageData" ref="schoolUpload"></schoolUpload>
  </div>
</template>

<script>
import { schoolPage, deleteSchool, schoolDownload } from '../../api/school'
import { provinceList } from '../../api/province'
import qs from 'qs';
import Pagination from '../../components/Pagination'
import { loadToken } from '../../utils/util'
import schoolInsert from './schoolInsert'
import schoolUpdate from './schoolUpdate'
import schoolDetail from './schoolDetail'
import schoolUpload from './schoolUpload'

export default {
  data() {
    return {
      loading: false,
      formInline: {
        current: 1,
        size: 10,
        chineseName: undefined,   // 学校中文名称
        englishName: undefined,
        website: undefined,
        provinceId: undefined,    // 省份ID
        token: loadToken(),
        orders: [],
      },
      userEntity: undefined,
      listData: [], //分页数据
      multipleSelection: [], //多选数据
      provinceList: [], //省份列表
      // page param
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 2
      }
    }
  },
  components: {
    Pagination,
    schoolInsert,
    schoolUpdate,
    schoolDetail,
    schoolUpload
  },

  created() {
    // 加载用户缓存
    this.userEntity = JSON.parse(localStorage.getItem('userEntity'));
    // 加载省份列表
    this.loadProvinceList();
    // 分页数据
    this.getPageData()
  },
  methods: {
    // 加载省份列表
    loadProvinceList() {
      provinceList().then(res => {
        this.provinceList = res.data || [];
      }).catch(err => {
        console.error('加载省份列表失败：', err);
      });
    },
    // page-1: page
    getPageData(parameter) {
      this.loading = true
      if (!parameter){
        parameter = this.formInline;
      }
      schoolPage(parameter)
        .then(res => {
          this.loading = false;
          this.listData = res.data.records
          this.pageParam.currentPage = res.data.current;
          this.pageParam.pageSize = res.data.size
          this.pageParam.total = res.data.total
        })
    },
    // page-2: callBack
    callback_getPageData(parm) {
      this.formInline.current = parm.currentPage
      this.formInline.size = parm.pageSize
      this.getPageData()
    },
    // page-3: page && sort
    getSortPageData(column){
      if (column.order != null){
        let sortProp = column.prop.replace(/([A-Z])/g, "_$1").toLowerCase();
        let orderBy = {"column": sortProp, "asc": column.order == 'ascending'};
        this.formInline.orders[0] = orderBy;
      } else {
        this.formInline.orders = [];
      }
      this.getPageData();
    },
    // search-1
    search() {
      this.getPageData()
    },
    // search-2
    resetSearch() {
      this.formInline.chineseName = undefined     // 学校中文名称
      this.formInline.englishName = undefined
      this.formInline.website = undefined
      this.formInline.provinceId = undefined      // 省份ID
    },
    handleAdd() {
      this.$refs.schoolInsert.show();
    },
    handleImport() {
      this.$refs.schoolUpload.show();
    },
    handleExport() {
      this.$confirm('确认导出当前查询到的数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
          const params = qs.stringify(this.formInline, { allowDots: true, skipNulls: true })
          let url = "/api/school/download?" + params
          window.open(url);
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消导出'
        });
      });
    },
    handleView(row) {
      this.$refs.schoolDetail.show(row);
    },
    handleEdit(row) {
      this.$refs.schoolUpdate.show(row);
    },
    handleDelete(row) {
      this.$confirm('确认删除该学校?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSchool({idList: [row.schoolId]})
          .then(res => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.getPageData();
          })
          .catch(err => {
            this.$message.error('删除失败：' + err.message);
          });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleBatchDelete() {
      this.$confirm('确认删除选中的学校?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const idList = this.multipleSelection.map(item => item.schoolId);
        deleteSchool({idList: idList})
          .then(res => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.getPageData();
          })
          .catch(err => {
            this.$message.error('删除失败：' + err.message);
          });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    getProvinceName(provinceId) {
      const province = this.provinceList.find(item => item.provinceId === provinceId);
      return province ? province.chineseName : '未知省份'; // 如果找不到，返回'未知省份'
    }
  }
}
</script>

<style scoped>
.user-search {
  margin-top: 20px;
}

.user-search .el-input--small {
    width: 180px!important;
}

.el-link {
  margin-left: 10px!important;
  cursor: pointer!important;
}

.inline-edit-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.inline-edit-form .el-form-item {
  margin-bottom: 0;
}
</style>
