<template>
  <div>

    <!-- page -->
    <!-- <Pagination v-bind:child-msg="pageParam" :layout="'total'" @callback_getPageData="callback_getPageData"></Pagination> -->

    <!--list-->
    <el-table size="small" ref="subTaskIds" :data="listData" highlight-current-row
      v-loading="loading" border element-loading-text="loading" style="width: 100%;">
      <el-table-column prop="subTaskId" label="子任务编号" width="90" show-overflow-tooltip></el-table-column>
      <!-- <el-table-column prop="startTime" label="开始时间" width="150"></el-table-column> -->
      <el-table-column prop="url" label="网址" width="50" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link :href="scope.row.url" target="_blank" :underline="false">
            <i class="el-icon-link"></i>
          </el-link>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="markdownRequest" label="Markdown请求" width="150" show-overflow-tooltip></el-table-column> -->
      <el-table-column prop="markdownResponse" label="Markdown网页内容" width="250" show-overflow-tooltip></el-table-column>
      <el-table-column prop="aiContent" label="联系人信息" width="250" show-overflow-tooltip></el-table-column>
      <el-table-column prop="endTime" label="结束时间" width="140"></el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <el-tooltip v-if="scope.row.status === 'FAIL' && scope.row.failRemark"
                      :content="scope.row.failRemark"
                      placement="top">
            <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
          </el-tooltip>
          <el-tag v-else :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="left" label="操作" min-width="100">
        <template slot-scope="scope">
          <el-link>
            <i class="el-icon-receiving" @click="popWin('subTaskRecordDetail', scope.row)"> 详情</i>
          </el-link>
          <el-link v-if="scope.row.status === 'FAIL' || scope.row.status === 'STOP'">
            <i class="el-icon-refresh" @click="retrySubTask(scope.row)"> 重试</i>
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <!-- Detail -->
    <subTaskRecordDetail @callback="getPageData" ref="subTaskRecordDetail"></subTaskRecordDetail>
  </div>
</template>

<script>
import { subTaskRecordPage, subTaskRecordReborn } from '../../api/subTaskRecord'
import Pagination from '../../components/Pagination'
import { loadToken } from '../../utils/util'
import subTaskRecordDetail from './subTaskRecordDetail'

export default {
  name: 'SubTaskRecord',
  props: {
    taskId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      listData: [], //分页数据
      // page param
      pageParam: {
        currentPage: 1,
        pageSize: 10000,
        total: 0
      }
    }
  },
  components: {
    Pagination,
    subTaskRecordDetail
  },

  watch: {
    taskId: {
      handler(newVal) {
        if (newVal) {
          this.getPageData()
        }
      },
      immediate: true
    }
  },

  methods: {
     // 获取状态显示样式
     getStatusType(status) {
      switch (status) {
        case 'RUN': return 'primary';
        case 'FAIL': return 'danger';
        case 'SUCCESS': return 'success';
        case 'REBORN': return 'info';
        case 'STOP': return 'warning';
        default: return 'info';
      }
    },
    // 获取状态显示文本
    getStatusText(status) {
      switch (status) {
        case 'RUN': return '运行中';
        case 'FAIL': return '失败';
        case 'SUCCESS': return '成功';
        case 'REBORN': return '已重试';
        case 'STOP': return '中断';
        default: return status;
      }
    },
    // page-1: page
    getPageData() {
      if (!this.taskId) return;

      this.loading = true
      const parameter = {
        current: this.pageParam.currentPage,
        size: this.pageParam.pageSize,
        taskId: this.taskId,
        token: loadToken()
      };

      subTaskRecordPage(parameter)
        .then(res => {
          this.loading = false;
          this.listData = res.data.records
          this.pageParam.currentPage = res.data.current;
          this.pageParam.pageSize = res.data.size
          this.pageParam.total = res.data.total
        })
        .catch(error => {
          this.loading = false;
          console.error('获取子任务记录失败:', error);
        })
    },
    // page-2: callBack
    callback_getPageData(parm) {
      this.pageParam.currentPage = parm.currentPage
      this.pageParam.pageSize = parm.pageSize
      this.getPageData()
    },
    // 弹窗
    popWin(windownName, data) {
      if (this.$refs[windownName]) {
        this.$refs[windownName].show(data);
      }
    },
    // 重试子任务
    retrySubTask(subTask) {
      this.$confirm(`确定要重试子任务 ${subTask.subTaskId} 吗？`, '确认重试', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.doRetrySubTask(subTask);
      }).catch(() => {
        this.$message.info('已取消重试');
      });
    },
    // 执行重试子任务
    doRetrySubTask(subTask) {
      const params = {
        subTaskId: subTask.subTaskId,
        token: loadToken()
      };

      subTaskRecordReborn(params)
        .then(res => {
          this.$message.success('子任务重试成功');
          // 刷新列表
          this.getPageData();
        })
        .catch(error => {
          console.error('重试子任务失败:', error);
          this.$message.error('重试子任务失败');
        });
    }
  }
}
</script>

<style scoped>
.el-link {
  margin-left: 10px !important;
  cursor: pointer !important;
}

/* 表格头黑底白字样式 */
::v-deep .el-table__header-wrapper .el-table__header th {
  background-color: #000000 !important;
  color: #ffffff !important;
}

::v-deep .el-table__header-wrapper .el-table__header th .cell {
  color: #ffffff !important;
}

/* 表格边框线加深样式 */
::v-deep .el-table {
  border-color: #333333 !important;
}

::v-deep .el-table td,
::v-deep .el-table th {
  border-color: #333333 !important;
}

::v-deep .el-table--border .el-table__inner-wrapper::after {
  border-color: #333333 !important;
}

::v-deep .el-table--border::after {
  border-color: #333333 !important;
}

/* /deep/ .el-pagination__total {
  text-align: center!important;
  display: block!important;
} */
</style>
