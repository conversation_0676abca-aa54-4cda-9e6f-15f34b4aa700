package com.my.college.mybatis.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 任务记录表
 *
 * <AUTHOR>
 * @date 2025-05-03
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("task_record")
public class TaskRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务编号
     */
    @TableId(value = "task_id", type = IdType.INPUT)
    private String taskId;
    
    /**
     * 批次号
     */
    @TableField(value = "batch_number")
    private Integer batchNumber;

    /**
     * 学校ID
     */
    @TableField(value = "school_id")
    private Integer schoolId;

    /**
     * 任务开始时间
     */
    @TableField(value = "start_time")
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    @TableField(value = "end_time")
    private LocalDateTime endTime;

    /**
     * 任务完成时间
     */
    @TableField(value = "finish_time")
    private LocalDateTime finishTime;

    /**
     * 任务状态(RUN, FAIL, SUCCESS, REBORN)
     */
    @TableField(value = "status")
    private String status;
    
    /**
     * 任务失败备注
     */
    @TableField(value = "fail_remark")
    private String failRemark;
    
    /**
     * 请求参数
     */
    private String request;
    
    /**
     * 响应内容
     */
    private String response;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
