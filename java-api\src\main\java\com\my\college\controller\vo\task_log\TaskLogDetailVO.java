package com.my.college.controller.vo.task_log;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_任务日志表
 *
 * <AUTHOR>
 * @date 2025-05-03
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class TaskLogDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增日志ID
     */
    private Integer logId;

    /**
     * 关联的任务编号
     */
    private String taskId;

    /**
     * 日志详细内容
     */
    private String content;
    
    /**
     * 日志类型(ERROR/WARNING/SUCCESS/INFO)
     */
    private String type;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
