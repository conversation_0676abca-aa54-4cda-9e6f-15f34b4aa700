import { reqGet, reqPut, reqDelete, reqPost, reqUpload } from './axiosFun';

// 分页查询通讯录
export const addressBookPage = (params) => { return reqGet("/address-book/page", params) };

// 获取详情
export const addressBookDetail = (params) => { return reqGet("/address-book/" + params.id) };

// 创建通讯录记录
export const createAddressBook = (params) => {
  const data = {
    schoolId: params.schoolId,
    department: params.department,
    contactName: params.contactName,
    email: params.email,
    position: params.position,
    manualFlag: params.manualFlag
  };
  return reqPost("/address-book", data);
};

// 更新通讯录记录
export const updateAddressBook = (params) => {
  const data = {
    id: params.id,
    schoolId: params.schoolId,
    department: params.department,
    contactName: params.contactName,
    email: params.email,
    position: params.position,
    manualFlag: params.manualFlag
  };
  return reqPut("/address-book", data);
};

// 删除通讯录记录
export const deleteAddressBook = (params) => { return reqDelete("/address-book", params) };

// 获取学校列表（用于下拉选择）
export const getSchoolList = () => { return reqGet("/school/list") };

// 下载
export const adressBookDownload = (params) => { return reqGet("/address-book/download", params) };

//  上传学校
export const adressBookUpload = (params) => { return reqUpload("/address-book/upload", params) };