package com.my.college.controller.dto.province;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_美国50个州州名
 *
 * <AUTHOR>
 * @date 2025-05-25
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class ProvinceUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字母缩写
     */
	@NotNull(message="provinceId不能为空")
    private String provinceId;

    /**
     * 英文名称
     */
	@NotNull(message="englishName不能为空")
    private String englishName;

    /**
     * 中文名称
     */
	@NotNull(message="chineseName不能为空")
    private String chineseName;

}
