import { reqGet, reqPut, reqDelete, reqPost, reqUpload } from './axiosFun';

// 分页
export const subTaskRecordPage = (params) => { return reqGet("/sub-task-record/page", params) };

// 获取详情
export const subTaskRecordDetail = (params) => { return reqGet("/sub-task-record/" + params.subTaskId) };

// 重试子任务
export const subTaskRecordReborn = (params) => { return reqPost("/sub-task-record/reborn", params) };
