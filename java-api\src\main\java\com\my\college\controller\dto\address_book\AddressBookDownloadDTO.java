package com.my.college.controller.dto.address_book;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import com.alibaba.fastjson2.annotation.JSONType;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 导出
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
public class AddressBookDownloadDTO 
						extends AddressBookPageDTO
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * token
     */
    @NotBlank(message = "token不能为空")
    private String token;


}