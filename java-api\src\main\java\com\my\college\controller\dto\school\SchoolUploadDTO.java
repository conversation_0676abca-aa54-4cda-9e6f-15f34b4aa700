package com.my.college.controller.dto.school;

import java.io.Serializable;
import java.util.Map;

import javax.validation.ConstraintViolationException;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson2.JSON;
import com.my.college.util.BeanValidators;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 上传并验证
 *
 * <AUTHOR>
 * @date 2025-04-13
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Slf4j
public class SchoolUploadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 省份
     */
    @ExcelProperty("省份")
    private String provinceId;

    /**
     * 中文名称
     */
    @NotBlank(message = "中文名称不能为空")
    @ExcelProperty("中文名称")
    private String chineseName;

    /**
     * 英文名称
     */
    @ExcelProperty("英文名称")
    private String englishName;

    /**
     * 学校网址
     */
    @NotBlank(message = "学校网址不能为空")
    @ExcelProperty("学校网址")
    @Pattern(regexp = "^(http|https)://[^\\s]*$", message = "学校网址格式有误")
    private String website;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

    
	/**
	 * 校验是否有效
	 * @param i 
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public String validate(int i) {
		try {
			BeanValidators.validateWithException(this);
			return StrUtil.EMPTY;
		} catch(ConstraintViolationException e) {
			Map<String, String> map = BeanUtil.copyProperties(this, Map.class);
			log.warn("【学校excel】数据行校验失败，忽略该行. {}, err:{}", JSON.toJSONString(map), e.getMessage());
			return StrUtil.format("第{}行:{}", (i+1), BeanValidators.extractPropertyAndMessageAsList(e));
		} catch(Exception e) {
			return StrUtil.format("第{}行:{}", (i+1), e.getMessage());
		}
	}
}
