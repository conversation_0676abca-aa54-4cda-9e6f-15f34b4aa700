package com.my.college.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.college.controller.dto.sub_task_record.SubTaskRecordInsertDTO;
import com.my.college.controller.dto.sub_task_record.SubTaskRecordPageDTO;
import com.my.college.controller.dto.sub_task_record.SubTaskRecordUpdateDTO;
import com.my.college.controller.vo.sub_task_record.SubTaskRecordDetailVO;
import com.my.college.controller.vo.sub_task_record.SubTaskRecordPageVO;
import com.my.college.forest.siliconflow.vo.ChatCompletionsVO;
import com.my.college.mybatis.entity.SubTaskRecord;

/**
 * 子任务记录表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-22
 */
public interface SubTaskRecordService extends IService<SubTaskRecord> {

	/**
	 * 新增
	 */
	void insert(SubTaskRecordInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(SubTaskRecordUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	SubTaskRecordDetailVO detail(String id);

	/**
	 * 分页
	 */
	Page<SubTaskRecordPageVO> page(SubTaskRecordPageDTO pageDTO);	

//	/**
//	 * 批量删除(物理删除)
//	 */
//	void delete(SubTaskRecordDeleteDTO deleteDTO);

	/**
	 * 创建子任务(状态为RUN)
	 */
	SubTaskRecord insert();	
	
	/**
	 * 子任务重生
	 * @param subTaskId
	 * @return
	 */
	SubTaskRecord reborn(String subTaskId);

	/**
	 * 子任务失败
	 */
	void fail(String failRemark);
	
	/**
	 * 更新markdown请求参数
	 * @param markdownRequest
	 */
	void updateMarkdownRequest(String markdownRequest);
	
	/**
	 * 更新markdown响应
	 * @param markdownResponse
	 */
	void updateMarkdownResponse(String markdownResponse);
	
	/**
	 * 更新ai请求参数
	 * @param response
	 */
	void updateAIRequest(String aiRequest);

	/**
	 * 仅更新ai响应字段
	 * @param response
	 */
	void updateAIResponse(String aiResponse);

	/**
	 * 更新ai响应结果的多个字段
	 * @param chatCompletionsVO
	 */
	void updateAIResult(ChatCompletionsVO chatCompletionsVO);


}
