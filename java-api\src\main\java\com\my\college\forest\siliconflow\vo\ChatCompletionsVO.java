package com.my.college.forest.siliconflow.vo;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson2.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor @AllArgsConstructor @Builder @Data 
public class ChatCompletionsVO {

    @JSONField(name = "choices", ordinal = 1)
    @Builder.Default
    List<Choices> choices = new ArrayList<Choices>();

    @JSONField(name = "created", ordinal = 2)
    String created;

    @JSONField(name = "id", ordinal = 3)
    String id;

    @JSONField(name = "model", ordinal = 4)
    String model;

    @JSONField(name = "object", ordinal = 5)
    String object;

    @JSONField(name = "usage", ordinal = 6)
    @Builder.Default
    Usage usage = new Usage();
    
    /** 异常信息 */
    String message;
    
    /** 异常数据 */
    String data;


    // ======== 以下为内部类 =========
    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
    public static class Function {

        @JSONField(name = "arguments", ordinal = 1)
        String arguments;

        @JSONField(name = "name", ordinal = 2)
        String name;
    }

    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
    public static class Tool_calls {

        @JSONField(name = "function", ordinal = 1)
        @Builder.Default
        Function function = new Function();

        @JSONField(name = "id", ordinal = 2)
        String id;

        @JSONField(name = "type", ordinal = 3)
        String type;
    }

    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
    public static class Message {

        @JSONField(name = "content", ordinal = 1)
        String content;

        @JSONField(name = "reasoning_content", ordinal = 2)
        String reasoning_content;

        @JSONField(name = "role", ordinal = 3)
        String role;

        @JSONField(name = "tool_calls", ordinal = 4)
        @Builder.Default
        List<Tool_calls> tool_calls = new ArrayList<Tool_calls>();
    }

    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
    public static class Choices {

        @JSONField(name = "finish_reason", ordinal = 1)
        String finish_reason;

        @JSONField(name = "message", ordinal = 2)
        @Builder.Default
        Message message = new Message();
    }

    @NoArgsConstructor @AllArgsConstructor @Builder @Data 
    public static class Usage {

        @JSONField(name = "completion_tokens", ordinal = 1)
        String completion_tokens;

        @JSONField(name = "prompt_tokens", ordinal = 2)
        String prompt_tokens;

        @JSONField(name = "total_tokens", ordinal = 3)
        String total_tokens;
    }

}