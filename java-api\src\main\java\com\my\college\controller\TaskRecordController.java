package com.my.college.controller;


import java.util.List;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.college.controller.dto.task_record.TaskRecordCreateBatchTaskDTO;
import com.my.college.controller.dto.task_record.TaskRecordPageDTO;
import com.my.college.controller.dto.task_record.TaskRecordRebornDTO;
import com.my.college.controller.vo.StdResp;
import com.my.college.controller.vo.task_log.TaskLogDetailVO;
import com.my.college.controller.vo.task_record.TaskRecordDetailVO;
import com.my.college.controller.vo.task_record.TaskRecordPageVO;
import com.my.college.service.TaskLogService;
import com.my.college.service.TaskRecordService;
import com.my.college.task.TaskEntrance;

import lombok.RequiredArgsConstructor;

/**
 * 任务记录表 
 * 
 * <AUTHOR>
 * @date 2025-05-03
 */
@RestController
@RequestMapping("/api/task-record")
@RequiredArgsConstructor
public class TaskRecordController {

    private final TaskRecordService taskRecordService;
    private final TaskLogService taskLogService;
    private final TaskEntrance taskEntrance;
    

    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<TaskRecordDetailVO> detail(@PathVariable String id) {
    	return StdResp.success(this.taskRecordService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<TaskRecordPageVO>> page(TaskRecordPageDTO pageDTO) {
        Page<TaskRecordPageVO> page = this.taskRecordService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
     * 通过taskId查询所有日志，根据logId倒序排列
     * @param taskId 任务ID
     * @return 日志列表
     */
    @GetMapping("/logs")
    public StdResp<List<TaskLogDetailVO>> logs(@RequestParam String taskId) {
        List<TaskLogDetailVO> logList = this.taskLogService.list(taskId);
        return StdResp.success(logList);
    }
    
//    /**
//     * 创建单个任务 (暂未用到)
//     */
//    @PostMapping("/create-single-task")
//    public StdResp<Boolean> createSingleTask(@Valid @RequestBody TaskRecordCreateSingleTaskDTO dto) {
//    	this.taskProcess.createSingleTask(dto.getSchoolId());
//    	return StdResp.success(Boolean.TRUE);
//    }
    
    /**
     * 创建批量任务
     */
    @PostMapping("/create-batch-task")
    public StdResp<Boolean> createBatchTask(@Valid @RequestBody TaskRecordCreateBatchTaskDTO dto) {
    	this.taskEntrance.batchCreate(dto.getSchoolIdList());
    	return StdResp.success(Boolean.TRUE);
    }
    
    /**
     * 任务重生 (用于重试失败的任务)
     */
    @PostMapping("/reborn")
    public StdResp<Boolean> reborn(@Valid @RequestBody TaskRecordRebornDTO dto) {
    	this.taskEntrance.reborn(dto.getTaskId());
    	return StdResp.success(Boolean.TRUE);
    }
}
