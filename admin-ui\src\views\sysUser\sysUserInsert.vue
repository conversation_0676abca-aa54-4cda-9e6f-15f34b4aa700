<template>
  <!-- 新增 -->
  <el-dialog title="新增用户" :visible.sync="editFormVisible" width="30%" @click="closeDialog">
    <el-form label-width="100px" :model="editForm" :rules="rules" ref="editForm">
      <el-form-item label="用户名" prop="username">
        <el-input size="small" v-model="editForm.username" placeholder="请输入用户名" clearable></el-input>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input size="small" v-model="editForm.password" placeholder="请输入密码" show-password clearable></el-input>
      </el-form-item>
      <el-form-item label="管理员" prop="adminFlag">
        <el-switch v-model="editForm.adminFlag"></el-switch>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input size="small" v-model="editForm.remark" placeholder="请输入备注" type="textarea" :rows="3" clearable></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="closeDialog">关闭</el-button>
      <el-button size="small" type="primary" :loading="loading" class="title" @click="submitForm('editForm')">提交</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { sysUserInsert } from '../../api/SysUser'

export default {
  name: 'sysUserInsert',
  props: ['childMsg'],
  data() {
    return {
      loading: false,
      editFormVisible: false,
      editForm: {
        username: '',
        password: '',
        adminFlag: false,
        remark: '',
      },
      rules: {
        username: [
          { required: true, message: '用户名必填', trigger: 'blur' },
          { min: 3, max: 32, message: '长度在 3 到 32 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '密码必填', trigger: 'blur' },
          { min: 6, max: 32, message: '长度在 6 到 32 个字符', trigger: 'blur' }
        ],
        remark: [
          { max: 255, message: '备注长度不超过 255 个字符', trigger: 'blur' }
        ],
      },
    }
  },

  // 方法
  methods: {
    submitForm(editData) {
      let self = this;
      this.$refs[editData].validate(valid => {
        if (!valid) {
          return false;
        }
        self.loading = true;
        sysUserInsert(this.editForm)
          .then(res => {
            self.editFormVisible = false
            self.loading = false
            self.$emit('callback')
            self.$message({ type: 'success', message: '添加成功' })
          })
          .catch(() => {
            self.loading = false;
          });
      })
    },
    closeDialog() {
      this.editFormVisible = false
    },
    show(row) {
      this.editFormVisible = true;
      this.loading = false
      this.resetForm();
      if (row) {
        Object.assign(this.editForm, row);
      }
    },
    resetForm() {
      this.editForm = {
        username: '',
        password: '',
        adminFlag: false,
        remark: '',
      };
      if (this.$refs.editForm) {
        this.$refs.editForm.resetFields();
      }
    },
  }
}
</script>

<style>
.el-dialog .el-dialog__body {
  padding: 0px 20px !important;
}
</style>
