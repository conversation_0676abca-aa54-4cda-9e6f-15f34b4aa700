package com.my.college.controller.dto.sub_task_log;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_子任务日志表
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SubTaskLogInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 关联的任务编号
     */
	@NotNull(message="subTaskId不能为空")
    private String subTaskId;

    /**
     * 日志类型(ERROR/WARNING/SUCCESS/INFO)
     */
	@NotNull(message="type不能为空")
    private String type;

    /**
     * 日志详细内容
     */
	@NotNull(message="content不能为空")
    private String content;

    /**
     * 创建时间
     */
	@NotNull(message="createTime不能为空")
    private LocalDateTime createTime;

}
