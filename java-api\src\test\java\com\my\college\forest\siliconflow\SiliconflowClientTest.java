package com.my.college.forest.siliconflow;

import java.math.BigDecimal;
import java.util.List;

import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson2.JSON;
import com.dtflys.forest.http.ForestRequest;
import com.my.college.controller.vo.sys_param.SysParamVO;
import com.my.college.forest.siliconflow.dto.ChatCompletionsDTO;
import com.my.college.forest.siliconflow.dto.component.Message;
import com.my.college.forest.siliconflow.onerrror.SiliconflowOnError;
import com.my.college.forest.siliconflow.vo.ChatCompletionsVO;
import com.my.college.mybatis.entity.SubTaskRecord;
import com.my.college.service.SubTaskRecordService;
import com.my.college.service.SysParamService;

import lombok.extern.slf4j.Slf4j;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
class SiliconflowClientTest {

	@Autowired
	SiliconflowClient siliconflowClient;
	
	@Autowired
	SysParamService sysParamService;
	
	@Autowired
	SubTaskRecordService subTaskRecordService; 
	
	String apiKey;
	SiliconflowOnError onErr;
	
	@BeforeEach
	void init() {
		this.apiKey = "sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud";
		this.onErr = new SiliconflowOnError();
	}
	
	@Test
	void testChatCompletions() {
		// 系统参数
		SysParamVO sysParam = this.sysParamService.get();
		String model = sysParam.getAiModel();
		String aiApiKey = sysParam.getAiApiKey();
		Integer aiTimeout = new Integer(sysParam.getAiTimeout());
		String aiUserPrompt = sysParam.getAiUserPrompt();
		// DTO参数
		String subTaskId = "1937287745735516160";
		SubTaskRecord subTaskRecord = this.subTaskRecordService.getById(subTaskId);
		String markdownResponse = subTaskRecord.getMarkdownResponse();
		Double temperature = 0.01D;
		aiUserPrompt = aiUserPrompt.replaceAll("\\$\\{content\\}", java.util.regex.Matcher.quoteReplacement(markdownResponse));
		List<Message> messages = Lists.newArrayList(
				Message.user(aiUserPrompt)
		);
		ChatCompletionsDTO chatCompletionsDTO = ChatCompletionsDTO.builder()
			.model(model)
			.messages(messages)
			.temperature(temperature) 
			.build();
		// 请求 (带超时参数)
		ForestRequest<ChatCompletionsVO> req = this.siliconflowClient.chatCompletions(chatCompletionsDTO, aiApiKey, this.onErr);
		req.setConnectTimeout(aiTimeout * 1000);
		req.setReadTimeout(aiTimeout * 1000);
		ChatCompletionsVO vo = req.execute(ChatCompletionsVO.class);
		if (this.onErr.isError()) {
			log.info(this.onErr.getErrMsg());
		} else {
			log.info(JSON.toJSONString(vo));
		}
	}

	@Test
	void testGetBalance() {
		BigDecimal balance = this.siliconflowClient.getBalance(this.apiKey, this.onErr);
		log.info("{} 元", balance);
	}
	
	@Test
	void testWrongApiKey() {
		BigDecimal balance = this.siliconflowClient.getBalance("heheh", this.onErr);
		if (this.onErr.isError()) {
			log.info(this.onErr.getErrMsg());
		} else {
			log.info("{} 元", balance);
		}
	}

}
