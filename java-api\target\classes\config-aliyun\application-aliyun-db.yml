### mysql数据源
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************************************************
    username: college
    password: college
    druid:
      # 连接池的配置信息
      # 初始化大小，最小，最大
      initial-size: 1
      min-idle: 1
      maxActive: 2
      # 检测连接是否有效的SQL
      validation-query: SELECT 1
      # 申请连接时执行validationQuery检测连接是否有效
      test-on-borrow: false
      # 归还连接时执行validationQuery检测连接是否有效
      test-on-return: false
      # 空闲连接检测
      test-while-idle: true
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
        
### mp配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/*.xml
  type-aliases-package: com.my.college.mybatis.entity
  global-config:
    db-config:
      id-type: AUTO
      table-underline: true
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl  #打印SQL和参数      
    
#仅打印SQL和总记录数
logging:
  level:
    com.my.college.mybatis.mapper: debug     