package com.my.college.task.subtask;

import java.util.concurrent.CompletableFuture;

import org.springframework.boot.logging.LogLevel;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;
import com.my.college.controller.vo.sys_param.SysParamVO;
import com.my.college.exception.BusinessException;
import com.my.college.forest.markdown.MarkdownClient;
import com.my.college.forest.markdown.dto.MarkdownDTO;
import com.my.college.forest.markdown.onerror.MarkdownOnError;
import com.my.college.forest.markdown.vo.MarkdownVO;
import com.my.college.service.SubTaskLogService;
import com.my.college.service.SubTaskRecordService;
import com.my.college.task.TaskContext;

import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 子任务处理（获取markdown）
 * <AUTHOR>
 *
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SubTaskProcess {

	private final MarkdownClient markdownClient; 
	private final SubTaskRecordService subTaskRecordService;
	private final SubTaskLogService subTaskLogService;
	private final AIProcess aiProcess;

	
	/**
	 * 执行子任务处理流程
	 * @param batchNumber 
	 * @param school 
	 * @param sysParam
	 */
	public void process(String taskId, String url, SysParamVO sysParam) {
		String subTaskId = IdUtil.getSnowflakeNextIdStr();
		SubTaskContext.set(taskId, subTaskId, url);
		
		// 创建任务(状态为RUN)
		this.subTaskRecordService.insert();
		
		// [请求.1] 构造请求参数(markdown)
		MarkdownDTO markdownDTO = this.buildParam(url);
		String request = JSON.toJSONString(markdownDTO);
		this.subTaskRecordService.updateMarkdownRequest(request);

		// 异步请求
		this.asyncProcess(markdownDTO);
	}
	
	/**
	 * 异步请求
	 * @param markdownDTO
	 */
	public void asyncProcess(MarkdownDTO markdownDTO) {
		String currentTaskId = SubTaskContext.getTaskId();
		String currentSubTaskId = SubTaskContext.getSubTaskId();
		String currentUrl = SubTaskContext.getUrl();
		
		// 异步调用接口
		CompletableFuture.supplyAsync(() -> {
			return this.sendReqeust(markdownDTO, currentTaskId, currentSubTaskId, currentUrl);
		}).thenAccept(response -> {
			this.onSuccess(response, currentTaskId, currentSubTaskId, currentUrl);
		}).exceptionally(e -> {
			this.onError(e, currentTaskId, currentSubTaskId, currentUrl);
			return null;
		});
	}
	
	/**
	 * [请求.1]构造请求参数(markdown)
	 */
	private MarkdownDTO buildParam(String url) {
		return MarkdownDTO.builder()
					.url(url)
					.build();
	}
	
	/**
	 * [请求.2]发送请求, 等待返回
	 */
	private MarkdownVO sendReqeust(MarkdownDTO markdownDTO, 
							String currentTaskId, String currentSubTaskId, String currentUrl) {
		// 沿用旧的上下文
		SubTaskContext.set(currentTaskId, currentSubTaskId, currentUrl);
		try {
			MarkdownOnError onError = new MarkdownOnError();
			MarkdownVO vo = this.markdownClient.markdown(markdownDTO, onError);
			this.subTaskLogService.save(LogLevel.INFO, "接口调用成功, 等待响应");
			BusinessException.when(vo == null, onError.getErrMsg());
			return vo;
		} finally {
			TaskContext.remove();
		}
	}
	
	/**
	 * [请求.3]接口响应成功
	 */
	private void onSuccess(MarkdownVO markdownVO,
							String currentTaskId, String currentSubTaskId, String currentUrl) {
		// 沿用旧的上下文
		SubTaskContext.set(currentTaskId, currentSubTaskId, currentUrl);
		try {
			// 保存markdown
			String markdown = markdownVO.getMarkdown_content();
			this.subTaskRecordService.updateMarkdownResponse(markdown);
		
			// 交给AI处理
			this.aiProcess.process(currentTaskId, currentSubTaskId, currentUrl, markdown);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			this.subTaskRecordService.fail("markdown抓取后运算失败: " + e.getMessage());
		} finally {
			TaskContext.remove();
		}
	}
	
	/**
	 * [请求.4]接口响应异常
	 */
	private void onError(Throwable e,
						String currentTaskId, String currentSubTaskId, String currentUrl) {
		// 沿用旧的上下文
		SubTaskContext.set(currentTaskId, currentSubTaskId, currentUrl);
		try {
			// 更新任务状态为失败
			this.subTaskRecordService.fail("markdown接口调用失败: " + e.getMessage());
		} finally {
			TaskContext.remove();
		}
	}
	
}
