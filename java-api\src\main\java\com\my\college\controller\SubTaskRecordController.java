package com.my.college.controller;


import java.util.List;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.college.controller.dto.sub_task_record.SubTaskRecordPageDTO;
import com.my.college.controller.dto.sub_task_record.SubTaskRecordRebornDTO;
import com.my.college.controller.dto.task_record.TaskRecordRebornDTO;
import com.my.college.controller.vo.StdResp;
import com.my.college.controller.vo.sub_task_log.SubTaskLogDetailVO;
import com.my.college.controller.vo.sub_task_record.SubTaskRecordDetailVO;
import com.my.college.controller.vo.sub_task_record.SubTaskRecordPageVO;
import com.my.college.service.SubTaskLogService;
import com.my.college.service.SubTaskRecordService;
import com.my.college.task.subtask.SubTaskEntrance;
import com.my.college.task.subtask.SubTaskProcess;

import lombok.RequiredArgsConstructor;

/**
 * 子任务记录表 
 * 
 * <AUTHOR>
 * @date 2025-06-22
 */
@RestController
@RequestMapping("/api/sub-task-record")
@RequiredArgsConstructor
public class SubTaskRecordController {

    private final SubTaskRecordService subTaskRecordService;
    private final SubTaskLogService subTaskLogService;
    private final SubTaskEntrance subTaskEntrance;
    

    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<SubTaskRecordDetailVO> detail(@PathVariable String id) {
    	return StdResp.success(this.subTaskRecordService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<SubTaskRecordPageVO>> page(SubTaskRecordPageDTO pageDTO) {
        Page<SubTaskRecordPageVO> page = this.subTaskRecordService.page(pageDTO);
        return StdResp.success(page);
    }
    
//    /**
//    * 批量删除(物理删除)
//    */
//    @DeleteMapping
//    public StdResp<?> delete(@Valid @RequestBody SubTaskRecordDeleteDTO deleteDTO) {
//    	this.subTaskRecordService.delete(deleteDTO);
//		return StdResp.success();
//    }
    
    /**
     * 通过taskId查询所有日志，根据logId倒序排列
     * @param subTaskId 子任务ID
     * @return 日志列表
     */
    @GetMapping("/logs")
    public StdResp<List<SubTaskLogDetailVO>> logs(@RequestParam String subTaskId) {
        List<SubTaskLogDetailVO> logList = this.subTaskLogService.list(subTaskId);
        return StdResp.success(logList);
    }
    
    
    /**
     * 任务重生 (用于重试失败的子任务)
     */
    @PostMapping("/reborn")
    public StdResp<Boolean> reborn(@Valid @RequestBody SubTaskRecordRebornDTO dto) {
    	this.subTaskEntrance.reborn(dto.getSubTaskId());
    	return StdResp.success(Boolean.TRUE);
    }
}
