package com.my.college.controller.dto.school;

import java.io.Serializable;

import com.alibaba.fastjson2.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.college.controller.vo.school.SchoolPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_学校表
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SchoolPageDTO 
						extends PageDTO<SchoolPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer schoolId;

    /**
     * 省份缩写
     */
    private String provinceId;
    
    /**
     * 中文名称
     */
    private String chineseName;

    /**
     * 英文名称
     */
    private String englishName;

    /**
     * 网址
     */
    private String website;

    /**
     * 备注
     */
    private String remark;

}
