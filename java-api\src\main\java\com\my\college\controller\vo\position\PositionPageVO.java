package com.my.college.controller.vo.position;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 查看实体详情_职位表
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class PositionPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 职位
     */
    private String position;

}
