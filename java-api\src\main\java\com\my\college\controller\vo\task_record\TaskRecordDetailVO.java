package com.my.college.controller.vo.task_record;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 查看实体详情_任务记录表
 *
 * <AUTHOR>
 * @date 2025-05-03
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class TaskRecordDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务编号 
     */
    private String taskId;
    
    /**
     * 批次号
     */
    private Integer batchNumber;
    
    /**
     * 学校ID
     */
    private Integer schoolId;
    
    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    private LocalDateTime endTime;

    /**
     * 任务完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 任务状态(RUN, FAIL, SUCCESS)
     */
    private String status;
    
    /**
     * 任务失败备注
     */
    private String failRemark;

    /**
     * 请求参数(状态)
     */
    private String requestArgStatus;

    /**
     * 请求参数(策略)
     */
    private String requestArgStrategy;

    /**
     * 请求参数(模板)
     */
    private String requestArgCmdTpl;

    /**
     * 请求参数(用户提示词)
     */
    private String requestArgUserPrompt;

    /**
     * 响应内容(csv数据)
     */
    private String responseCsv;

    /**
     * 响应内容(推理详情)
     */
    private String responseReasoning;
    
    /**
     * 响应内容(token消耗情况)
     */
    private String responseUsage;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
