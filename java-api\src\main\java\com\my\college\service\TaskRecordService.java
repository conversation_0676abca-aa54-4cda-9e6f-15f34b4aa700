package com.my.college.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.college.controller.dto.task_record.TaskRecordPageDTO;
import com.my.college.controller.vo.task_record.TaskRecordDetailVO;
import com.my.college.controller.vo.task_record.TaskRecordPageVO;
import com.my.college.mybatis.entity.TaskRecord;

/**
 * 任务记录表 服务类
 *
 * <AUTHOR>
 * @date 2025-05-03
 */
public interface TaskRecordService extends IService<TaskRecord> {

	/**
	 * 查询详情
	 * @param id 主键
	 */
	TaskRecordDetailVO detail(String id);

	/**
	 * 分页
	 */
	Page<TaskRecordPageVO> page(TaskRecordPageDTO pageDTO);	
	
	/**
	 * 创建任务
	 * @return
	 */
	TaskRecord insert();

	/**
	 * 任务失败
	 */
	void fail(String failRemark);

	/**
	 * 任务重生
	 * @param taskId
	 */
	TaskRecord reborn(String taskId);
	
	/**
	 * 更新请求参数
	 * @param request
	 */
	void updateRequest(String request);

	/**
	 * 更新响应内容
	 * @param response
	 */
	void updateResponse(String response);

	/**
	 * 任务处理完毕
	 */
	void succeed();

	/**
	 * 如果子任务全部完成，则主任务完成
	 * @param string 
	 */
	void succeedWhenSubtaskSucceed(String taskId);

}
