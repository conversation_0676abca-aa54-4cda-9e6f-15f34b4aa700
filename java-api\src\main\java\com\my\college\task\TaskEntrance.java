package com.my.college.task;

import java.util.List;

import org.springframework.stereotype.Component;

import com.my.college.controller.vo.sys_param.SysParamVO;
import com.my.college.exception.BusinessException;
import com.my.college.mybatis.entity.School;
import com.my.college.mybatis.entity.TaskRecord;
import com.my.college.service.SchoolService;
import com.my.college.service.SysParamService;
import com.my.college.service.TaskRecordService;
import com.my.college.service.check.result.CheckResult;

import cn.hutool.core.collection.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务管理入口
 * <AUTHOR>
 *
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TaskEntrance {

	private final SysParamService sysParamService;
	private final TaskRecordService taskRecordService;
	private final SchoolService schoolService;
	private final TaskProcess taskProcess;

	
//	/**
//	 * 创建单个任务（暂未用到）
//	 */
//	public void createSingleTask(Integer schoolId) {
//		SysParamVO sysParam = this.checkSysParam();
//		Integer batchNumber = this.sysParamService.nextBatchNumber();
//		School school = this.schoolService.getById(schoolId);
//		this.taskProcess.process(batchNumber, school, sysParam);
//	}
	
	/**
	 * 创建批量任务
	 */
	public void batchCreate(List<Integer> schoolIdList) {
		SysParamVO sysParam = this.checkSysParam();
		Integer batchNumber = this.sysParamService.nextBatchNumber();
		List<School> schoolEntityList = this.schoolService.listByIds(schoolIdList);
		for (School school : schoolEntityList) {
			this.taskProcess.process(batchNumber, school, sysParam);
		}
	}

	/**
	 * 任务重生
	 * @param taskId
	 */
	public void reborn(String taskId) {
		// 任务状态:已重试
		TaskRecord taskRecord = this.taskRecordService.reborn(taskId);
		// 新任务（旧批次号）
		Integer batchNumber = taskRecord.getBatchNumber();
		School school = this.schoolService.getById(taskRecord.getSchoolId());
		SysParamVO sysParam = this.checkSysParam();
		this.taskProcess.process(batchNumber, school, sysParam);
	}
	
	
	/**
	 * 读取并检测系统参数
	 */
	private SysParamVO checkSysParam() {
        List<CheckResult> invalidItems = this.sysParamService.get().check().invalidItems();
		boolean allValid = CollectionUtil.isEmpty(invalidItems);
        if (!allValid) {
        	log.error("系统参数检测结果: 未通过");
            for (CheckResult item : invalidItems) {
            	log.error("- ({}){}: {} ", item.getName(), item.getLabel(), item.getMessage());
            }
            BusinessException.by("系统参数检测未通过!");
            return null;
        } else {
        	log.info("系统参数检测结果: 通过");
        	return this.sysParamService.get();
        }
	}
  
}
