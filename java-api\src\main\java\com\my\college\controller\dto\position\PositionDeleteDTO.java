package com.my.college.controller.dto.position;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotEmpty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批量删除_职位表
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PositionDeleteDTO 
						implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	* 主键数组
	*/
	@NotEmpty(message = "idList不能为空")
	private List<String> idList;
	
}
