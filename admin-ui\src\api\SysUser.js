import { reqGet, reqPut, reqDelete, reqPost } from './axiosFun';

// 修改我的密码
export const sysUserChangeMyPassword = (params) => { return reqPut("/sys-user/change-my-password", params) };

// 修改我的资料
export const sysUserChangeMyProfile = (params) => { return reqPut("/sys-user/change-my-profile", params) };

// 重置密码
export const sysUserResetPassword = (params) => { return reqPut("/sys-user/reset-password", params) };

// 分页查询用户列表
export const sysUserPage = (params) => { return reqGet("/sys-user/page", params) };

// 删除用户（根据用户名）
export const sysUserDelete = (params) => { return reqDelete("/sys-user", params) };

// 新增用户
export const sysUserInsert = (params) => { return reqPost("/sys-user", params) };

// 修改用户资料
export const sysUserChangeProfile = (params) => { return reqPut("/sys-user/change-profile", params) };

// 停用、启用
export const sysUserChangeEnable = (params) => { return reqPut("/sys-user/change-enable", params) };

// 查看我的资料
export const sysUserProfile = (params) => { return reqGet("/sys-user/profile", params) };

// 修改密码（根据用户名）
export const changePassword = (params) => { return reqPut("/sys-user/change-password", params) };

// 修改管理员状态（根据用户名）
export const changeAdmin = (params) => { return reqPut("/sys-user/change-admin", params) };


