package com.my.college.cache;

import java.lang.reflect.Field;
import java.util.List;

import javax.annotation.PostConstruct;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.my.college.controller.vo.sys_param.SysParamVO;
import com.my.college.enums.SysParamFieldEnum;
import com.my.college.mybatis.entity.SysParam;
import com.my.college.mybatis.mapper.SysParamMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 参数表缓存
 * <AUTHOR>
 * @date 2025年6月13日
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SysParamCache {
	
	private final SysParamMapper sysParamMapper;

	private SysParamVO vo;
	
	
	/**
	 * 初始缓存
	 */
	@PostConstruct
	public void reloadCache() {
		this.vo = new SysParamVO();
		List<SysParam> entityList = this.sysParamMapper.selectList(new LambdaQueryWrapper<SysParam>());
		
		try {
			// 通过反射给vo的各个字段赋值
			Class<?> voClass = this.vo.getClass();
			
			// 遍历所有的参数枚举
			for (SysParamFieldEnum fieldEnum : SysParamFieldEnum.values()) {
				String fieldName = fieldEnum.name();
				
				// 从数据库查询结果中找到对应的参数值
				String fieldValue = entityList.stream()
					.filter(entity -> fieldName.equals(entity.getK()))
					.map(SysParam::getV)
					.findFirst()
					.orElse(null);
				
				if (fieldValue != null) {
					// 通过反射设置字段值
					Field field = voClass.getDeclaredField(fieldName);
					field.setAccessible(true);
					field.set(this.vo, fieldValue);
				}
			}
			// 赋值给成员变量
			log.info("系统参数缓存重载完成. SysParam: {}", JSON.toJSONString(this.vo));
		} catch (Exception e) {
			log.error("系统参数缓存重载失败", e);
			throw new RuntimeException("系统参数缓存重载失败", e);
		}
	}
	
	/**
	 * 配置
	 * @return
	 */
	public SysParamVO get() {
		return this.vo;
	}

	
}
