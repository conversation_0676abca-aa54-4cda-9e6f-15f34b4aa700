package com.my.college.forest.google.onerror;

import com.my.college.enums.annotation.StringEnum;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 谷歌异常枚举
 * <AUTHOR>
 *
 */
@RequiredArgsConstructor
public enum CustomSearchErrCode 
						implements StringEnum {

	E000("网络异常"),
	
	E011("搜索引擎ID不正确"),
	E012("API密钥不正确"),
	E013("免费账号达到每日接口限额"),
	E014("收费账号达到每日接口限额"),
	
	E020("响应内容解析失败"),
	;
	
	@Getter
	private final String label;
	
	
	/**
	 * 返回编号和文本
	 * @return
	 */
	public String getCodeAndLabel() {
		return StrUtil.format("[{}]{}", this.name(), this.label);
	}
}
