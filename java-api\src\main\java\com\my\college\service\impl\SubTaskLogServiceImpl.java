package com.my.college.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.boot.logging.LogLevel;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.college.controller.vo.sub_task_log.SubTaskLogDetailVO;
import com.my.college.mybatis.entity.SubTaskLog;
import com.my.college.mybatis.mapper.SubTaskLogMapper;
import com.my.college.service.SubTaskLogService;
import com.my.college.task.subtask.SubTaskContext;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 子任务日志表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Service
@Slf4j
public class SubTaskLogServiceImpl extends ServiceImpl<SubTaskLogMapper, SubTaskLog> implements SubTaskLogService {


	/**
	 * 记录子任务日志
	 * @param content 日志内容
	 */
	@Override
	public void save(LogLevel level, String content) {
		// 写日志文件
		switch (level) {
			case INFO:
				log.info("[{}/{}] {}", SubTaskContext.getTaskId(), SubTaskContext.getSubTaskId(), content);
				break;
			case WARN:
				log.warn("[{}/{}] {}", SubTaskContext.getTaskId(), SubTaskContext.getSubTaskId(), content);
				break;
			case ERROR:
				log.error("[{}/{}] {}", SubTaskContext.getTaskId(), SubTaskContext.getSubTaskId(), content);
				break;
			default:
				break;
		}
		// 写表
		SubTaskLog logDTO = SubTaskLog.builder()
				.subTaskId(SubTaskContext.getSubTaskId())
				.type(level.name())
				.content(content)
				.createTime(LocalDateTime.now())
				.build();
		this.save(logDTO);
	}
	
	@Override
	public List<SubTaskLogDetailVO> list(String subTaskId) {
		// 倒序查询
		LambdaQueryWrapper<SubTaskLog> queryWrapper = new LambdaQueryWrapper<SubTaskLog>()
			.eq(SubTaskLog::getSubTaskId, subTaskId)
			.orderByDesc(SubTaskLog::getLogId);
		
		List<SubTaskLog> logList = this.baseMapper.selectList(queryWrapper);
		return logList.stream()
				.map(taskLog -> BeanUtil.copyProperties(taskLog, SubTaskLogDetailVO.class))
				.collect(Collectors.toList());
	}
	
}
