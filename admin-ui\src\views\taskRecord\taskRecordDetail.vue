<template>
  <!-- 详情 -->
  <el-dialog :title="'任务编号： ' + readForm.taskId" :visible.sync="editFormVisible" width="50%" @click="closeDialog">
    <!-- <el-descriptions class="margin-top" title="" :column="1" border>
      <el-descriptions-item>
          <template slot="label">任务编号</template>{{readForm.taskId}}
      </el-descriptions-item>
    </el-descriptions> -->

    <el-descriptions class="margin-top" title="" :column="3" border>
      <el-descriptions-item>
          <template slot="label">任务状态</template>
          <el-tag :type="getStatusType(readForm.status)">{{ getStatusText(readForm.status) }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item>
          <template slot="label">开始时间</template>{{readForm.startTime}}
      </el-descriptions-item>
      <el-descriptions-item>
          <template slot="label">结束时间</template>{{readForm.endTime}}
      </el-descriptions-item>
      <!-- <el-descriptions-item>
          <template slot="label">完成时间</template>{{readForm.finishTime}}
      </el-descriptions-item>
      <el-descriptions-item>
          <template slot="label">创建时间</template>{{readForm.createTime}}
      </el-descriptions-item>
      <el-descriptions-item>
          <template slot="label">更新时间</template>{{readForm.updateTime}}
      </el-descriptions-item> -->
    </el-descriptions>

    <el-tabs v-model="activeTab">
      <el-tab-pane label="Google搜索 - 请求 / 响应" name="request">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>请求</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="copyToClipboard(formatJsonDisplay(readForm.request))">复制</el-button>
          </div>
          <pre>{{ formatJsonDisplay(readForm.request) }}</pre>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>响应 (联系人网址)</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="copyToClipboard(formatJsonDisplay(readForm.response))">复制</el-button>
          </div>
          <pre>{{ formatJsonDisplay(readForm.response) }}</pre>
        </el-card>
      </el-tab-pane>

      <!-- <el-tab-pane label="响应内容" name="response">
        <el-card class="box-card" v-if="readForm.failRemark">
          <div slot="header" class="clearfix">
            <span>任务失败备注</span>
          </div>
          <pre>{{ readForm.failRemark || '无' }}</pre>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>数据</span>
          </div>
          <pre>{{ readForm.responseCsv || '无' }}</pre>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>推理详情</span>
          </div>
          <pre>{{ readForm.responseReasoning || '无' }}</pre>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>token消耗情况</span>
          </div>
          <pre>{{ formatJsonDisplay(readForm.responseUsage) }}</pre>
        </el-card>
      </el-tab-pane> -->

      <el-tab-pane label="任务日志" name="logs">
        <div v-loading="logsLoading">
          <el-timeline v-if="taskLogs && taskLogs.length > 0">
            <el-timeline-item
              v-for="(log, index) in taskLogs"
              :key="index"
              :timestamp="log.createTime"
              placement="top"
              :type="getLogType(log.type)">
              <el-card>
                <p>{{ log.content }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
          <el-empty v-else description="暂无日志数据"></el-empty>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加空白区域 -->
    <div class="bottom-space"></div>
  </el-dialog>
</template>

<script>
// 使用相对路径重新导入
import { taskRecordDetail } from '../../api/taskRecord'
import { taskRecordLogs } from '../../api/taskRecord'

export default {
  name: 'TaskRecordDetail',
  props: ['childMsg'],
  data() {
    return {
      loading: false,
      logsLoading: false,
      editFormVisible: false,
      activeTab: 'request',
      taskLogs: [], // 存储任务日志
      readForm: {
        taskId: '',
        startTime: '',
        endTime: '',
        finishTime: '',
        status: '',
        failRemark: '',
        request: '',
        response: '',
        requestArgStatus: '',
        requestArgStrategy: '',
        requestArgCmdTpl: '',
        requestArgSystemPrompt: '',
        requestArgUserPrompt: '',
        responseCsv: '',
        responseReasoning: '',
        responseUsage: '',
        createTime: '',
        updateTime: ''
      }
    }
  },

  methods: {
    closeDialog() {
      this.editFormVisible = false
    },
     // 获取状态显示样式
     getStatusType(status) {
      switch (status) {
        case 'RUN': return 'primary';
        case 'FAIL': return 'danger';
        case 'SUCCESS': return 'success';
        case 'REBORN': return 'info';
        case 'STOP': return 'warning';
        default: return 'info';
      }
    },
    // 获取状态显示文本
    getStatusText(status) {
      switch (status) {
        case 'RUN': return '运行中';
        case 'FAIL': return '失败';
        case 'SUCCESS': return '成功';
        case 'REBORN': return '已重试';
        case 'STOP': return '中断';
        default: return status;
      }
    },
    // 获取日志类型样式
    getLogType(type) {
      switch(type) {
        case 'ERROR': return 'danger';
        case 'WARN': return 'warning';
        case 'SUCCESS': return 'success';
        default: return 'primary';
      }
    },
    // 格式化JSON显示
    formatJsonDisplay(jsonString) {
      try {
        if (!jsonString) return '无';

        // 如果已经是对象，直接格式化
        if (typeof jsonString === 'object') {
          return JSON.stringify(jsonString, null, 2);
        }

        // 如果是字符串，尝试解析
        if (typeof jsonString === 'string') {
          // 先检查是否是JSON字符串
          if (jsonString.trim().startsWith('{') || jsonString.trim().startsWith('[')) {
            const obj = JSON.parse(jsonString);
            return JSON.stringify(obj, null, 2);
          } else {
            // 普通字符串，直接返回但保持格式
            return jsonString;
          }
        }

        // 其他类型直接转换
        return JSON.stringify(jsonString, null, 2);
      } catch (e) {
        // 解析失败，返回原始内容
        return jsonString || '无';
      }
    },
    // 复制到剪贴板
    copyToClipboard(text) {
      if (!text || text === '无') {
        this.$message.warning('没有内容可以复制');
        return;
      }

      if (navigator.clipboard && window.isSecureContext) {
        // 现代浏览器的方法
        navigator.clipboard.writeText(text).then(() => {
          this.$message.success('已复制到剪贴板');
        }).catch(() => {
          this.fallbackCopyTextToClipboard(text);
        });
      } else {
        // 兼容性方法
        this.fallbackCopyTextToClipboard(text);
      }
    },
    // 兼容性复制方法
    fallbackCopyTextToClipboard(text) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.top = '0';
      textArea.style.left = '0';
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';

      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        document.execCommand('copy');
        this.$message.success('已复制到剪贴板');
      } catch (err) {
        this.$message.error('复制失败，请手动复制');
      }

      document.body.removeChild(textArea);
    },
    // 获取任务日志
    fetchTaskLogs() {
      if (!this.readForm.taskId) return;
      this.logsLoading = true;
      // 使用导入的API函数
      taskRecordLogs({ taskId: this.readForm.taskId })
        .then(res => {
          console.log('日志数据响应:', res);
          this.logsLoading = false;
          if (res.success) {
            this.taskLogs = res.data || [];
          } else {
            this.$message.error(res.message || '获取任务日志失败');
          }
        })
        .catch(error => {
          console.error('获取日志错误详情:', error);
          this.logsLoading = false;
          this.$message.error('获取任务日志失败');
        });
    },
    show(row) {
      this.editFormVisible = true;
      this.loading = true;
      Object.assign(this.readForm, row);
      this.activeTab = 'request';
    }
  },
  watch: {
    activeTab(newVal) {
      // 当切换到日志标签页时，加载日志数据
      if (newVal === 'logs') {
        this.fetchTaskLogs();
      }
    }
  }
}
</script>

<style>
.el-dialog {
  margin-top: 7vh !important;
}

.el-dialog .el-dialog__body {
  padding: 0px 20px !important;
}

.bottom-space {
  height: 20px;
  width: 100%;
}

.box-card {
  margin-bottom: 15px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
  margin: 1em 15px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  border: 1px solid #e4e7ed;
  color: #2c3e50;
}

/* JSON语法高亮样式 */
pre .json-key {
  color: #e74c3c;
  font-weight: bold;
}

pre .json-string {
  color: #27ae60;
}

pre .json-number {
  color: #3498db;
}

pre .json-boolean {
  color: #9b59b6;
}

pre .json-null {
  color: #95a5a6;
  font-style: italic;
}

.el-card__body {
    padding: 0px;
}


.el-card__header {
  padding: 10px 20px;
}

/* 日志时间线样式 */
.el-timeline-item__timestamp {
  font-size: 13px !important;
}

.el-card.is-always-shadow, .el-card.is-hover-shadow:focus, .el-card.is-hover-shadow:hover {
    -webkit-box-shadow: 0 0 0 0 rgb(255 255 255 / 10%);
}
</style>
