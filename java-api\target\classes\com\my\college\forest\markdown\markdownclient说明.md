提交单个任务

curl -X POST http://localhost:5000/api/v2/submit \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://www.example.com",
    "timeout": 60,
    "keep_link": false,
    "priority": 2
  }'

响应：
{
  "success": true,
  "task_id": "abc123-def456-ghi789",
  "message": "任务已提交到队列",
  "estimated_wait_time": 5.2
}
============

查询任务状态
curl http://localhost:5000/api/v2/task/{task_id}

响应示例：
{
  "success": true,
  "task_status": {
    "task_id": "abc123-def456-ghi789",
    "url": "https://www.example.com",
    "status": "completed",  // pending, processing, completed, failed
    "created_at": 1640995200.0,
    "started_at": 1640995202.0,
    "completed_at": 1640995210.0,
    "execution_time": 8.0,
    "result": {
      "success": true,
      "markdown_content": "页面内容...",
      "method": "crawl4ai"
    }
  }
}

任务状态说明
| 状态 | 说明 | 下一步 |
|------|------|--------|
| pending | 任务在队列中等待 | 继续等待 |
| processing | 任务正在处理中 | 继续等待 |
| completed | 任务成功完成 | 获取结果 |
| failed | 任务执行失败 | 查看错误信息 |
| retrying | 任务重试中 | 继续等待 |