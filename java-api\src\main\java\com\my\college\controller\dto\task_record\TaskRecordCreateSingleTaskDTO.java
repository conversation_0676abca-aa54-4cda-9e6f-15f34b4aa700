package com.my.college.controller.dto.task_record;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 创建单个任务
 *
 * <AUTHOR>
 * @date 2025-05-03
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class TaskRecordCreateSingleTaskDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * schoolId
     */
	@NotNull(message="schoolId不能为空")
    private Integer schoolId;


}
