package com.my.college.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.college.controller.dto.sys_user.SysUserChangeMyPasswordDTO;
import com.my.college.controller.dto.sys_user.SysUserChangePasswordDTO;
import com.my.college.controller.dto.sys_user.SysUserDeleteDTO;
import com.my.college.controller.dto.sys_user.SysUserInsertDTO;
import com.my.college.controller.dto.sys_user.SysUserPageDTO;
import com.my.college.controller.dto.sys_user.SysUserUpdateDTO;
import com.my.college.controller.vo.sys_user.SysUserPageVO;
import com.my.college.mybatis.entity.SysUser;
import com.my.college.mybatis.mapper.SysUserMapper;
import com.my.college.service.SysUserService;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.crypto.digest.DigestUtil;

/**
 * 用户信息表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {


	@Override
	public SysUser getByUsernameAndPassword(String username, String password) {
		// 使用MD5加密密码进行比较
		String encryptedPassword = DigestUtil.md5Hex(password);
		return this.baseMapper.getByUsernameAndPassword(username, encryptedPassword);
	}

	@Override
	public SysUser get(String userId) {
		return this.getById(userId);
	}

	@Override
	public void changeMyPassword(SysUserChangeMyPasswordDTO dto) {
		dto.validate();
		String username = StpUtil.getLoginIdAsString();
		String pwd = dto.getPassword();
		// 使用MD5加密密码
		String encryptedPwd = DigestUtil.md5Hex(pwd);
		this.baseMapper.updatePassword(username, encryptedPwd);
	}
	
	@Transactional
	@Override
	public void insert(SysUserInsertDTO insertDTO) {
		SysUser entity = BeanUtil.copyProperties(insertDTO, SysUser.class);
		String encryptedPassword = DigestUtil.md5Hex(insertDTO.getPassword());
		entity.setPassword(encryptedPassword);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(SysUserUpdateDTO updateDTO) {
		SysUser entity = BeanUtil.copyProperties(updateDTO, SysUser.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public Page<SysUserPageVO> page(SysUserPageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(SysUserDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}

	@Override
	public void changePassword(SysUserChangePasswordDTO dto) {
		String pwd = dto.getPassword();
		// 使用MD5加密密码
		String encryptedPwd = DigestUtil.md5Hex(pwd);
		this.baseMapper.updatePassword(dto.getUsername(), encryptedPwd);
	}
}
