package com.my.college.tool;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;

import cn.hutool.core.util.StrUtil;

/**
 * 数据工具
 * <AUTHOR>
 *
 */
public class CsvDataTool {

	/**
	 * 抽取csvData数据
	 * @param csvData
	 * @return
	 */
	public static String extract(String csvData) {
		if (StrUtil.isBlank(csvData)) {
			csvData = StrUtil.EMPTY;
		} else if (csvData.startsWith("```csv") && csvData.endsWith("```")) {
			csvData = csvData.substring(csvData.indexOf("```csv") + 6, csvData.lastIndexOf("```")).trim();
		}
		return csvData;
	}
	
	/**
	 * 逐行csv数据
	 * @param csvData
	 * @return
	 */
	public static List<List<String>> dataList(String csvData) {
		final List<List<String>> csvDataList = new ArrayList<>();
		final String[] csvLines = csvData.split("\n");
		for (String line : csvLines) {
			List<String> row = new ArrayList<>();
			String[] cols = line.split(",");
			for (String col : cols) {
				// 去除引号（如果有）
				if (col.startsWith("\"") && col.endsWith("\"")) {
					col = col.substring(1, col.length() - 1);
				}
				row.add(col);
			}
			csvDataList.add(row);
		}
		return csvDataList;
	}
	
	/**
	 * 读取模板文件内容(忽略表头)
	 * @param excelTplPath
	 * @return
	 * @throws IOException
	 * @throws FileNotFoundException
	 */
	public static List<Map<Integer, Object>> templateData(String excelTplPath) throws IOException, FileNotFoundException {
		final List<Map<Integer, Object>> templateData = new ArrayList<>();
		try (FileInputStream fis = new FileInputStream(excelTplPath)) {
			EasyExcel.read(fis).sheet().registerReadListener(new PageReadListener<Map<Integer, Object>>(dataList -> {
				templateData.addAll(dataList);
			})).doRead();
		}
		return templateData;
	}
}
