{"editor.tabSize": 2, "files.associations": {"*.vue": "vue"}, "eslint.autoFixOnSave": true, "eslint.options": {"extensions": [".js", ".vue"]}, "eslint.validate": ["javascript", "javascriptreact", "vue", "vue-html"], "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/dist": true}, "emmet.syntaxProfiles": {"javascript": "jsx", "vue": "html", "vue-html": "html"}, "git.confirmSync": false, "window.zoomLevel": 0, "vsicons.projectDetection.autoReload": true, "typescript.check.tscVersion": false, "editor.renderWhitespace": "boundary", "editor.cursorBlinking": "smooth", "workbench.colorTheme": "Solarized Light", "workbench.iconTheme": "vscode-great-icons", "editor.minimap.enabled": true, "editor.minimap.renderCharacters": false, "tslint.autoFixOnSave": true, "editor.fontFamily": "'Droid Sans Mono', 'Courier New', monospace, 'Droid Sans Fallback'", "beautify.tabSize": 2, "window.title": "${dirty}${activeEditorMedium}${separator}${rootName}", "typescript.extension.sortImports.maxNamedImportsInSingleLine": 5, "typescript.extension.sortImports.omitSemicolon": true, "editor.codeLens": true, "editor.snippetSuggestions": "top", "react-native-storybooks.port": 6006}