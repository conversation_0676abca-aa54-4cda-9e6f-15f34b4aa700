package com.my.college.controller.dto.sys_user;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotEmpty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批量删除_用户信息表
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUserDeleteDTO 
						implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	* 主键数组
	*/
	@NotEmpty(message = "idList不能为空")
	private List<String> idList;
	
}
