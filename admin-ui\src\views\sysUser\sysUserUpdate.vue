<template>
  <!-- 修改 -->
  <el-dialog title="修改用户" :visible.sync="editFormVisible" width="30%" @click="closeDialog">
    <el-form label-width="100px" :model="editForm" :rules="rules" ref="editForm">
      <el-form-item label="用户名" prop="username">
        <el-input size="small" v-model="editForm.username" placeholder="用户名" disabled></el-input>
      </el-form-item>
      <el-form-item label="管理员" prop="adminFlag">
        <el-switch v-model="editForm.adminFlag"></el-switch>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input size="small" v-model="editForm.remark" placeholder="请输入备注" type="textarea" :rows="3" clearable></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="closeDialog">关闭</el-button>
      <el-button size="small" type="primary" :loading="loading" class="title" @click="submitForm('editForm')">提交</el-button>
    </div>
  </el-dialog>
</template>

<script>
// 修正导入路径
import { reqPut } from '../../api/axiosFun'

export default {
  name: 'sysUserUpdate',
  props: ['childMsg'],
  data() {
    return {
      loading: false,
      editFormVisible: false,
      editForm: {
        username: '',
        adminFlag: false,
        remark: '',
      },
      rules: {
        remark: [
          { max: 255, message: '备注长度不超过 255 个字符', trigger: 'blur' }
        ],
      },
    }
  },

  // 方法
  methods: {
    submitForm(editData) {
      let self = this;
      this.$refs[editData].validate(valid => {
        if (!valid) {
          return false;
        }
        self.loading = true;
        reqPut("/sys-user/", this.editForm)
          .then(res => {
            self.editFormVisible = false
            self.loading = false
            self.$emit('callback')
            self.$message({ type: 'success', message: '修改成功' })
          })
          .catch(err => {
            self.loading = false;
            self.$message.error('修改失败，请检查网络或联系管理员');
          });
      })
    },
    closeDialog() {
      this.editFormVisible = false
    },
    show(row) {
      this.editFormVisible = true;
      this.loading = false
      this.resetForm();
      if (row) {
        Object.assign(this.editForm, row);
      }
    },
    resetForm() {
      this.editForm = {
        username: '',
        adminFlag: false,
        remark: '',
      };
      if (this.$refs.editForm) {
        this.$refs.editForm.resetFields();
      }
    }
  }
}
</script>

<style>
.el-dialog .el-dialog__body {
  padding: 0px 20px !important;
}
</style>
