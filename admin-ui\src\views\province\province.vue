<template>
  <div>
    <!-- breadcrumb -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">系统管理</el-breadcrumb-item>
      <el-breadcrumb-item>美国省份</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- search -->
    <el-form :inline="true" :model="formInline" class="user-search" ref="searchForm">
      <el-form-item label="查询：">
        <el-input size="small" v-model="formInline.provinceId" placeholder="缩写" clearable style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input size="small" v-model="formInline.englishName" placeholder="英文名称" clearable style="width: 130px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input size="small" v-model="formInline.chineseName" placeholder="中文名称" clearable style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" icon="el-icon-search" @click="search">查询</el-button>
        <el-button size="small" type="primary" icon="el-icon-refresh" @click="resetSearch()">重置</el-button>
      </el-form-item>
    </el-form>

    <!--list-->
    <el-table size="small" ref="ids" @sort-change="getSortPageData" :data="listData" highlight-current-row v-loading="loading" border element-loading-text="loading" style="width: 100%;">
      <el-table-column prop="provinceId" label="缩写" width="100" show-overflow-tooltip sortable="custom"></el-table-column>
      <el-table-column prop="englishName" label="英文名称" width="200" show-overflow-tooltip sortable="custom"></el-table-column>
      <el-table-column prop="chineseName" label="中文名称" width="200" show-overflow-tooltip sortable="custom"></el-table-column>
      <el-table-column align="left" label="操作" min-width="80">
        <template slot-scope="scope">
          <el-link v-if="userEntity && userEntity.adminFlag">
            <i class="el-icon-edit" @click="handleEdit(scope.row)"></i>
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <!-- page -->
    <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>

    <!-- 子组件 -->
    <provinceUpdate @callback="getPageData" ref="provinceUpdate"></provinceUpdate>
  </div>
</template>

<script>
import { provincePage } from '../../api/province'
import qs from 'qs';
import Pagination from '../../components/Pagination'
import { loadToken } from '../../utils/util'
import provinceUpdate from './provinceUpdate'

export default {
  data() {
    return {
      loading: false,
      formInline: {
        current: 1,
        size: 10,
        chineseName: undefined,   // 省份中文名称
        englishName: undefined,   // 省份英文名称
        provinceId: undefined,    // 省份编号
        token: loadToken(),
        orders: [],
      },
      userEntity: undefined,
      listData: [], //分页数据
      // page param
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  components: {
    Pagination,
    provinceUpdate
  },

  created() {
    // 加载用户缓存
    this.userEntity = JSON.parse(localStorage.getItem('userEntity'));
    // 分页数据
    this.getPageData()
  },
  methods: {
    // page-1: page
    getPageData(parameter) {
      this.loading = true
      if (!parameter){
        parameter = this.formInline;
      }
      provincePage(parameter)
        .then(res => {
          this.loading = false;
          this.listData = res.data.records
          this.pageParam.currentPage = res.data.current;
          this.pageParam.pageSize = res.data.size
          this.pageParam.total = res.data.total
        })
    },
    // page-2: callBack
    callback_getPageData(parm) {
      this.formInline.current = parm.currentPage
      this.formInline.size = parm.pageSize
      this.getPageData()
    },
    // page-3: page && sort
    getSortPageData(column){
      if (column.order != null){
        let sortProp = column.prop.replace(/([A-Z])/g, "_$1").toLowerCase();
        let orderBy = {"column": sortProp, "asc": column.order == 'ascending'};
        this.formInline.orders[0] = orderBy;
      } else {
        this.formInline.orders = [];
      }
      this.getPageData();
    },
    // search-1
    search() {
      this.getPageData()
    },
    // search-2
    resetSearch() {
      this.formInline.chineseName = undefined     // 省份中文名称
      this.formInline.englishName = undefined     // 省份英文名称
      this.formInline.provinceId = undefined      // 省份编号
    },
    handleEdit(row) {
      this.$refs.provinceUpdate.show(row);
    }
  }
}
</script>

<style scoped>
.user-search {
  margin-top: 20px;
}

.user-search .el-input--small {
    width: 180px!important;
}

.el-link {
  margin-left: 10px!important;
  cursor: pointer!important;
}

.inline-edit-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.inline-edit-form .el-form-item {
  margin-bottom: 0;
}
</style>
