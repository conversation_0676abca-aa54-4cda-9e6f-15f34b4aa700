package com.my.college.service.impl;

import com.my.college.mybatis.entity.Dept;
import com.my.college.mybatis.mapper.DeptMapper;
import com.my.college.service.DeptService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.college.controller.dto.dept.DeptInsertDTO;
import com.my.college.controller.dto.dept.DeptPageDTO;
import com.my.college.controller.dto.dept.DeptUpdateDTO;
import com.my.college.controller.dto.dept.DeptDeleteDTO;
import com.my.college.controller.vo.dept.DeptDetailVO;
import com.my.college.controller.vo.dept.DeptPageVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.my.college.exception.BusinessException;
import org.springframework.transaction.annotation.Transactional;

/**
 * 部门表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class DeptServiceImpl extends ServiceImpl<DeptMapper, Dept> implements DeptService {


	@Transactional
	@Override
	public void insert(DeptInsertDTO insertDTO) {
		// 参数校验
		if (insertDTO == null || StrUtil.isBlank(insertDTO.getDept())) {
			BusinessException.by("部门名称不能为空");
		}

		// 检查部门是否已存在
		Dept existingDept = this.baseMapper.selectById(insertDTO.getDept());
		if (existingDept != null) {
			BusinessException.by("部门 [{}] 已存在", insertDTO.getDept());
		}

		// 创建新部门
		Dept entity = BeanUtil.copyProperties(insertDTO, Dept.class);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(DeptUpdateDTO updateDTO) {
		Dept entity = BeanUtil.copyProperties(updateDTO, Dept.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public DeptDetailVO detail(String id) {
		Dept entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, DeptDetailVO.class);
	}

	@Override
	public Page<DeptPageVO> page(DeptPageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(DeptDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}	
}
