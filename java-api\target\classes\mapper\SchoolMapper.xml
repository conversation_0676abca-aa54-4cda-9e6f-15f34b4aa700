<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.college.mybatis.mapper.SchoolMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.college.mybatis.entity.School">
        <id column="school_id" property="schoolId" />
        <result column="province_id" property="provinceId" />
        <result column="chinese_name" property="chineseName" />
        <result column="english_name" property="englishName" />
        <result column="website" property="website" />
        <result column="remark" property="remark" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        school_id, province_id, chinese_name, english_name, website, remark, update_time
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.college.controller.vo.school.SchoolPageVO">
		SELECT
			school_id, province_id, chinese_name, english_name, website, remark, update_time
		FROM
			school AS t1
		<where>
        	1=1
	        <if test="schoolId != null and schoolId != ''">
	           	AND t1.school_id = #{schoolId}
            </if>
	        <if test="provinceId != null and provinceId != ''">
	           	AND t1.province_id = #{provinceId}
            </if>
	        <if test="chineseName != null and chineseName != ''">
	           	AND t1.chinese_name = #{chineseName}
            </if>
	        <if test="englishName != null and englishName != ''">
	           	AND t1.english_name = #{englishName}
            </if>
	        <if test="website != null and website != ''">
	           	AND t1.website = #{website}
            </if>
	        <if test="remark != null and remark != ''">
	           	AND t1.remark = #{remark}
            </if>
        </where>
    </select>

</mapper>
