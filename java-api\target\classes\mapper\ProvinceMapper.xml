<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.college.mybatis.mapper.ProvinceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.college.mybatis.entity.Province">
        <id column="province_id" property="provinceId" />
        <result column="english_name" property="englishName" />
        <result column="chinese_name" property="chineseName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        province_id, english_name, chinese_name
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.college.controller.vo.province.ProvincePageVO">
		SELECT
			province_id, english_name, chinese_name
		FROM
			province AS t1
		<where>
        	1=1
	        <if test="provinceId != null and provinceId != ''">
	           	AND t1.province_id = #{provinceId}
            </if>
	        <if test="englishName != null and englishName != ''">
	           	AND t1.english_name = #{englishName}
            </if>
	        <if test="chineseName != null and chineseName != ''">
	           	AND t1.chinese_name = #{chineseName}
            </if>
        </where>
    </select>

</mapper>
