package com.my.college.forest.deepseek.dto;

import java.util.List;

import com.my.college.forest.deepseek.dto.format.DeepSeekResponseFormat;
import com.my.college.forest.deepseek.dto.message.DeepSeekMessage;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChatCompletionsDTO {
	
	String model;
	
	List<DeepSeekMessage> messages;
	
	Integer maxTokens;
	
	Double temperature;
	
	DeepSeekResponseFormat responseFormat;
	
	String apiKey;
	

}
