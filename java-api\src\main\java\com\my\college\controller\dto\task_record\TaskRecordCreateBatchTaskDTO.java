package com.my.college.controller.dto.task_record;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotEmpty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 创建批量任务
 *
 * <AUTHOR>
 * @date 2025-05-03
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class TaskRecordCreateBatchTaskDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * schoolIdList
     */
	@NotEmpty(message="schoolIdList不能为空")
    private List<Integer> schoolIdList;

}
