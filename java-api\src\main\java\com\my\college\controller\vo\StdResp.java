package com.my.college.controller.vo;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 标准Json响应
 * @date 2024.04.13
 * @param <T> 对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StdResp<T> implements Serializable {

	private static final long serialVersionUID = -4107852542596783815L;
	

	Boolean success;
	
	String errCode;
	
	String errMsg;
	
	T data;
	
	
	/**
	 * 成功响应
	 * @return
	 */
	public static <T> StdResp<T> success(){
		StdResp<T> resp = new StdResp<T>();
		resp.success = true;
		return resp;
	}

	/**
	 * 成功响应
	 * @param <T> 数据对象
	 * @return
	 */
	public static <T> StdResp<T> success(T t){
		StdResp<T> resp = new StdResp<T>();
		resp.success = true;
		resp.data = t;
		return resp;
	}
	
	/**
	 * 失败响应
	 * @param <T>
	 * @param errCode
	 * @return
	 */
	public static <T> StdResp<T> fail(String errCode){
		StdResp<T> resp = new StdResp<T>();
		resp.success = false;
		resp.errCode = errCode;
		return resp;
	}
	
	/**
	 * 失败响应
	 * @param errCode 异常码
	 * @param errMsg  异常信息
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static StdResp fail(String errCode, String errMsg){
		StdResp resp = new StdResp();
		resp.success = false;
		resp.errCode = errCode;
		resp.errMsg = errMsg;
		return resp;
	}
}
