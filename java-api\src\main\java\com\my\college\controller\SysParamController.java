package com.my.college.controller;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.my.college.controller.dto.sys_param.SysParamUpdateDTO;
import com.my.college.controller.vo.StdResp;
import com.my.college.controller.vo.sys_param.SysParamCheckVO;
import com.my.college.controller.vo.sys_param.SysParamVO;
import com.my.college.forest.markdown.address.MarkdownAddress;
import com.my.college.service.SysParamService;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * 系统参数配置表
 * 
 * <AUTHOR>
 * @date 2025-05-01
 */
@RestController
@RequestMapping("/api/sys-param")
@RequiredArgsConstructor
public class SysParamController {

	private final SysParamService sysParamService;
	private final MarkdownAddress markdownAddress;
	
	/** 默认的谷歌搜索关键词 */
	private final String DEFAULT_GOOGLE_SERACH_KEYWORD = "site:学校网址 intext:(职位清单) (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") intitle:(\"staff\" OR \"contact\" OR \"directory\" OR \"people\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc";

	/** 默认的AI用户提示词 */
	private final String DEFAULT_AI_USER_PROMPT = "Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.";

	

	/**
	 * 修改
	 */
	@PutMapping
	public StdResp<?> update(@Valid @RequestBody SysParamUpdateDTO updateDTO) {
		this.sysParamService.update(updateDTO);
		return StdResp.success();
	}

	/**
	 * 根据主键查询
	 * 
	 * @param id 主键
	 */
	@GetMapping("detail")
	public StdResp<SysParamVO> detail() {
		return StdResp.success(this.sysParamService.get());
	}

	/**
	 * 检查参数
	 */
	@GetMapping("/check")
	public StdResp<SysParamCheckVO> check() {
		SysParamVO sysParamVO = this.sysParamService.get();
		return StdResp.success(sysParamVO.check());
	}
	
	/**
	 * markdown服务器地址
	 */
	@GetMapping("/markdown-server-url")
	public StdResp<String> markdownServerUrl() {
		String url = StrUtil.format("{}://{}:{}", 
						this.markdownAddress.getScheme(), 
						this.markdownAddress.getHost(),
						this.markdownAddress.getPort());
		return StdResp.success(url);
	}

	
}
