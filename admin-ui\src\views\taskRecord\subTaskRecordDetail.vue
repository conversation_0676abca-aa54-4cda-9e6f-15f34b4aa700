<template>
  <!-- 详情 -->
  <el-dialog :title="'子任务编号： ' + readForm.subTaskId" :visible.sync="editFormVisible" width="60%" @click="closeDialog">
    <el-descriptions class="margin-top" title="" :column="3" border>
      <el-descriptions-item>
          <template slot="label">子任务状态</template>
          <el-tag :type="getStatusType(readForm.status)">{{ getStatusText(readForm.status) }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item>
          <template slot="label">开始时间</template>{{readForm.startTime}}
      </el-descriptions-item>
      <el-descriptions-item>
          <template slot="label">结束时间</template>{{readForm.endTime}}
      </el-descriptions-item>
      <el-descriptions-item>
          <template slot="label">网址</template>
          <el-link :href="readForm.url" target="_blank" type="primary" :underline="false">{{ readForm.url }}</el-link>
      </el-descriptions-item>
    </el-descriptions>

    <el-tabs v-model="activeTab">
      <el-tab-pane label="Markdown - 请求 / 响应" name="markdown">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>Markdown请求</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="copyToClipboard(readForm.markdownRequest)">复制</el-button>
          </div>
          <pre>{{ readForm.markdownRequest || '无' }}</pre>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>Markdown响应</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="copyToClipboard(readForm.markdownResponse)">复制</el-button>
          </div>
          <pre>{{ readForm.markdownResponse || '无' }}</pre>
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="AI - 请求 / 响应" name="ai">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>AI请求</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="copyToClipboard(formatJsonDisplay(readForm.aiRequest))">复制</el-button>
          </div>
          <pre>{{ formatJsonDisplay(readForm.aiRequest) }}</pre>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>AI响应</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="copyToClipboard(formatJsonDisplay(readForm.aiContent))">复制</el-button>
          </div>
          <pre>{{ formatJsonDisplay(readForm.aiContent) }}</pre>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>AI推理</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="copyToClipboard(formatJsonDisplay(readForm.aiReasoning))">复制</el-button>
          </div>
          <pre>{{ formatJsonDisplay(readForm.aiReasoning) }}</pre>
        </el-card>
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>AI token 使用</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="copyToClipboard(formatJsonDisplay(readForm.aiTokenUsage))">复制</el-button>
          </div>
          <pre>{{ formatJsonDisplay(readForm.aiTokenUsage) }}</pre>
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="任务日志" name="logs">
        <div v-loading="logsLoading">
          <el-timeline v-if="taskLogs && taskLogs.length > 0">
            <el-timeline-item
              v-for="(log, index) in taskLogs"
              :key="index"
              :timestamp="log.createTime"
              placement="top"
              :type="getLogType(log.type)">
              <el-card>
                <p>{{ log.content }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
          <el-empty v-else description="暂无日志数据"></el-empty>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加空白区域 -->
    <div class="bottom-space"></div>
  </el-dialog>
</template>

<script>
import { subTaskRecordDetail } from '../../api/subTaskRecord'
import { subTaskRecordLogs } from '../../api/taskRecord'

export default {
  name: 'SubTaskRecordDetail',
  props: ['childMsg'],
  data() {
    return {
      loading: false,
      logsLoading: false,
      taskLogs: [],
      editFormVisible: false,
      activeTab: 'markdown',
      readForm: {
        subTaskId: '',
        taskId: '',
        startTime: '',
        endTime: '',
        url: '',
        markdownRequest: '',
        markdownResponse: '',
        aiRequest: '',
        aiContent: '',
        aiReasoning: '',
        aiTokenUsage: '',
        status: ''
      }
    }
  },

  watch: {
    // 监听选项卡切换
    activeTab(newTab) {
      if (newTab === 'logs' && this.readForm.subTaskId) {
        this.loadTaskLogs();
      }
    }
  },

  methods: {
    closeDialog() {
      this.editFormVisible = false
    },
    // 获取日志类型样式
    getLogType(type) {
      switch(type) {
        case 'ERROR': return 'danger';
        case 'WARN': return 'warning';
        case 'INFO': return 'primary';
        case 'SUCCESS': return 'success';
        default: return 'info';
      }
    },
    // 加载任务日志
    async loadTaskLogs() {
      if (!this.readForm.subTaskId) return;

      this.logsLoading = true;
      try {
        const response = await subTaskRecordLogs({ subTaskId: this.readForm.subTaskId });
        this.taskLogs = response.data || [];
      } catch (error) {
        console.error('加载日志失败:', error);
        this.$message.error('加载日志失败');
        this.taskLogs = [];
      } finally {
        this.logsLoading = false;
      }
    },
     // 获取状态显示样式
     getStatusType(status) {
      switch (status) {
        case 'RUN': return 'primary';
        case 'FAIL': return 'danger';
        case 'SUCCESS': return 'success';
        case 'REBORN': return 'info';
        case 'STOP': return 'warning';
        default: return 'info';
      }
    },
    // 获取状态显示文本
    getStatusText(status) {
      switch (status) {
        case 'RUN': return '运行中';
        case 'FAIL': return '失败';
        case 'SUCCESS': return '成功';
        case 'REBORN': return '已重试';
        case 'STOP': return '中断';
        default: return status;
      }
    },
    // 格式化JSON显示
    formatJsonDisplay(jsonString) {
      try {
        if (!jsonString) return '无';

        // 如果已经是对象，直接格式化
        if (typeof jsonString === 'object') {
          return JSON.stringify(jsonString, null, 2);
        }

        // 如果是字符串，尝试解析
        if (typeof jsonString === 'string') {
          // 先检查是否是JSON字符串
          if (jsonString.trim().startsWith('{') || jsonString.trim().startsWith('[')) {
            const obj = JSON.parse(jsonString);
            return JSON.stringify(obj, null, 2);
          } else {
            // 普通字符串，直接返回但保持格式
            return jsonString;
          }
        }

        // 其他类型直接转换
        return JSON.stringify(jsonString, null, 2);
      } catch (e) {
        // 解析失败，返回原始内容
        return jsonString || '无';
      }
    },
    // 复制到剪贴板
    copyToClipboard(text) {
      if (!text || text === '无') {
        this.$message.warning('没有内容可以复制');
        return;
      }

      if (navigator.clipboard && window.isSecureContext) {
        // 现代浏览器的方法
        navigator.clipboard.writeText(text).then(() => {
          this.$message.success('已复制到剪贴板');
        }).catch(() => {
          this.fallbackCopyTextToClipboard(text);
        });
      } else {
        // 兼容性方法
        this.fallbackCopyTextToClipboard(text);
      }
    },
    // 兼容性复制方法
    fallbackCopyTextToClipboard(text) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.top = '0';
      textArea.style.left = '0';
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';

      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        document.execCommand('copy');
        this.$message.success('已复制到剪贴板');
      } catch (err) {
        this.$message.error('复制失败，请手动复制');
      }

      document.body.removeChild(textArea);
    },
    show(row) {
      this.editFormVisible = true;
      this.loading = true;
      Object.assign(this.readForm, row);
      this.activeTab = 'markdown';
      // 加载日志数据
      this.loadTaskLogs();
    }
  }
}
</script>

<style>
.el-dialog {
  margin-top: 7vh !important;
}

.el-dialog .el-dialog__body {
  padding: 0px 20px !important;
}

.bottom-space {
  height: 20px;
  width: 100%;
}

.box-card {
  margin-bottom: 15px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
  margin: 1em 15px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  border: 1px solid #e4e7ed;
  color: #2c3e50;
}

.el-card__body {
    padding: 0px;
}

.el-card__header {
  padding: 10px 20px;
}

.el-card.is-always-shadow, .el-card.is-hover-shadow:focus, .el-card.is-hover-shadow:hover {
    -webkit-box-shadow: 0 0 0 0 rgb(255 255 255 / 10%);
}
</style>
