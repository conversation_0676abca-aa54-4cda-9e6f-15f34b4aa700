package com.my.college.service.check.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.my.college.service.check.ICheck;

/**
 * 字段检查注释
 * <AUTHOR>
 *
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Check {
	
	/**
	 * 使用哪个类检测
	 * @return
	 */
	 Class<? extends ICheck> chkClass();
	 
	/**
	 * 字段的中文标签名称
	 * @return
	 */
	String label();

}
