package com.my.college.util;

import java.util.LinkedList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.util.CollectionUtils;

import cn.hutool.core.util.StrUtil;



/**
 * 正则表达式工具<p/>
 * 静态方法1:getFirstMath() 得到一个匹配项	<br/>
 * 静态方法2:getMatchList() 得到多个匹配项
 */
public class RegExpUtil {

	/**
	 * 获得第一组匹配结果
	 * @param _regEx	正则表达式
	 * @param _body		待匹配的正文
	 * @return
	 */
	public static String getFirstMatch(String _regEx, CharSequence _body){
		return getMatchGroup(_regEx, _body, 1);
	}
	
	/**
	 * 是否全文匹配
	 * @param _regEx	正则表达式
	 * @param _body		待匹配的正文
	 * @return  是否全文匹配
	 */
	public static boolean test(String _regEx, CharSequence _body){
		Matcher m = Pattern.compile(_regEx).matcher(_body);
		return m.matches();
	}
	
	/**
	 * 是否找到一个以上匹配项
	 * @param _regEx	正则表达式
	 * @param _body		待匹配的正文
	 * @return  
	 */
	public static boolean find(String _regEx, CharSequence _body){
		Matcher m = Pattern.compile(_regEx).matcher(_body);
		return m.find();
	}
	
	/**
	 * 获得第x组匹配结果
	 * @param _regEx	正则表达式
	 * @param _body		待匹配的正文
	 * @param _groupIndex 组下标
	 * @return
	 */
	public static String getMatchGroup(String _regEx, CharSequence _body, int _groupIndex){
		if (StrUtil.isBlank(_regEx.toString()) || StrUtil.isBlank(_body.toString())){
			return "";
		}
		
		Matcher m = Pattern.compile(_regEx).matcher(_body);
		if (m.find()){
			return m.group(_groupIndex);		
		} else {
			return "";
		}
	}
	
	/**
	 * 获得所有匹配的结果
	 * @param regEx	正则表达式
	 * @param body	待匹配的正文
	 * @return
	 */
	public static List<String> getMatchList(String regEx, CharSequence body){
		if (null == regEx || "".equals(regEx)){
			return null;
		}
		
		List<String> list = new LinkedList<String>();
		Matcher m = Pattern.compile(regEx).matcher(body);
		while (m.find()){
			String s = m.group(0);
			list.add(s);
		}
		return list;
	}
	
	/**
	 * 获得所有匹配的结果
	 * @param _regEx	正则表达式
	 * @param _body		待匹配的正文
	 * @param _groupIndex 组下标
	 * @return
	 */
	public static List<String> getMatchList(String _regEx, CharSequence _body, int _groupIndex){
		if (null == _regEx || "".equals(_regEx)){
			return null;
		}
		
		List<String> list = new LinkedList<String>();
		Matcher m = Pattern.compile(_regEx).matcher(_body);
		while (m.find()){
			String s = m.group(_groupIndex);
			list.add(s);
		}
		return list;
	}	
	
	/**
	 * 替换所有
	 * @param _body		待匹配的正文
	 * @param _regEx	正则表达式
	 * @param _newStr	新字符
	 */
	public static String replaceAll(CharSequence _body, String _regEx, String _newStr){
		Matcher m = Pattern.compile(_regEx).matcher(_body);
		return m.replaceAll(_newStr);
	}
	
	/**
	 * 逐个替换
	 * @param groupIdx 要替换的组下标
	 * @return
	 */
	public static String replaceOneByOne(CharSequence body, String regEx, Integer groupIdx, List<String> covers) {
		List<String> matchList = getMatchList(regEx, body, groupIdx);
		if (CollectionUtils.isEmpty(matchList)) {
			throw new RuntimeException("原字符串中未找到匹配项!");
		}
		if (CollectionUtils.isEmpty(covers)) {
			throw new RuntimeException("covers值为空!");
		}
		if (matchList.size() != covers.size()) {
			throw new RuntimeException("匹配项数量与covers数量不一致!");
		}
		
		StringBuffer buffer = new StringBuffer();
		Matcher m = Pattern.compile(regEx).matcher(body);
		int idx = 0;
		while (m.find()){
			m.appendReplacement(buffer, covers.get(idx++));
		}
		m.appendTail(buffer);
		
		return buffer.toString();
	}

	/**
	 * 替换第一个
	 * @param _body		待匹配的正文
	 * @param _regEx	正则表达式
	 * @param _newStr	新字符
	 */
	public static String replaceFirst(CharSequence _body, String _regEx, String _newStr){
		Matcher m = Pattern.compile(_regEx).matcher(_body);
		return m.replaceFirst(_newStr);
	}
}
