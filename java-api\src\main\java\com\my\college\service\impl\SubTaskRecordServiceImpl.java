package com.my.college.service.impl;

import java.time.LocalDateTime;
import java.util.List;

import javax.annotation.PostConstruct;

import org.springframework.boot.logging.LogLevel;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.college.controller.dto.sub_task_record.SubTaskRecordInsertDTO;
import com.my.college.controller.dto.sub_task_record.SubTaskRecordPageDTO;
import com.my.college.controller.dto.sub_task_record.SubTaskRecordUpdateDTO;
import com.my.college.controller.vo.sub_task_record.SubTaskRecordDetailVO;
import com.my.college.controller.vo.sub_task_record.SubTaskRecordPageVO;
import com.my.college.enums.TaskStatus;
import com.my.college.forest.siliconflow.vo.ChatCompletionsVO;
import com.my.college.forest.siliconflow.vo.ChatCompletionsVO.Message;
import com.my.college.mybatis.entity.SubTaskRecord;
import com.my.college.mybatis.mapper.SubTaskRecordMapper;
import com.my.college.service.SubTaskLogService;
import com.my.college.service.SubTaskRecordService;
import com.my.college.service.TaskRecordService;
import com.my.college.task.subtask.SubTaskContext;
import com.my.college.util.ColumnLambda;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;

/**
 * 子任务记录表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-22
 */
@Service
@RequiredArgsConstructor
public class SubTaskRecordServiceImpl extends ServiceImpl<SubTaskRecordMapper, SubTaskRecord> implements SubTaskRecordService {

	private final SubTaskLogService subTaskLogService;
	private final TaskRecordService taskRecordService;
	
	
	// 启动时，将进行中的改为中断
	@PostConstruct
	public void init() {
		LambdaUpdateWrapper<SubTaskRecord> wrapper = new LambdaUpdateWrapper<SubTaskRecord>()
				.set(SubTaskRecord::getStatus, TaskStatus.STOP.name())
				.eq(SubTaskRecord::getStatus, TaskStatus.RUN.name());
		this.update(wrapper);
	}

	@Transactional
	@Override
	public void insert(SubTaskRecordInsertDTO insertDTO) {
		SubTaskRecord entity = BeanUtil.copyProperties(insertDTO, SubTaskRecord.class);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(SubTaskRecordUpdateDTO updateDTO) {
		SubTaskRecord entity = BeanUtil.copyProperties(updateDTO, SubTaskRecord.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public SubTaskRecordDetailVO detail(String id) {
		SubTaskRecord entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, SubTaskRecordDetailVO.class);
	}

	@Override
	public Page<SubTaskRecordPageVO> page(SubTaskRecordPageDTO pageDTO) {
		List<OrderItem> orders = pageDTO.getOrders();
		if (CollectionUtil.isEmpty(orders)) {
			orders.add(OrderItem.desc(new ColumnLambda<SubTaskRecord>().columnsToString(SubTaskRecord::getSubTaskId)));
		}
		return this.baseMapper.page(pageDTO);
	}

	@Override
	public SubTaskRecord insert() {
		SubTaskRecord subTaskRecord = SubTaskRecord.builder()
				.subTaskId(SubTaskContext.getSubTaskId())
				.taskId(SubTaskContext.getTaskId())
				.url(SubTaskContext.getUrl())
				.startTime(LocalDateTime.now())
				.status(TaskStatus.RUN.name())
				.build();
		this.save(subTaskRecord);
		
		// 记日志
		String content = "子任务创建成功";
		this.subTaskLogService.save(LogLevel.INFO, content);
		return subTaskRecord;
	}
	
//	@Transactional
//	@Override
//	public void delete(SubTaskRecordDeleteDTO deleteDTO) {
//		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
//	}

	@Override
	public SubTaskRecord reborn(String subTaskId) {
		SubTaskRecord dto = new SubTaskRecord();
		dto.setSubTaskId(subTaskId);
		dto.setStatus(TaskStatus.REBORN.name());
		this.updateById(dto);

		// 记日志
		SubTaskRecord entity = this.getById(subTaskId);
		SubTaskContext.set(entity.getTaskId(), entity.getSubTaskId(), entity.getUrl());
		this.subTaskLogService.save(LogLevel.INFO, "子任务已重试");
		return entity;
	}

	@Override
	public void fail(String failRemark) {
		SubTaskRecord entity = new SubTaskRecord();
		entity.setSubTaskId(SubTaskContext.getSubTaskId());
		entity.setStatus(TaskStatus.FAIL.name());
		entity.setEndTime(LocalDateTime.now());
		entity.setFailRemark(failRemark);
		this.updateById(entity);
		
		// 记日志
		String content = StrUtil.format("{}", failRemark);
		this.subTaskLogService.save(LogLevel.ERROR, content);
	}

	@Override
	public void updateMarkdownRequest(String markdownRequest) {
		SubTaskRecord entity = new SubTaskRecord();
		entity.setSubTaskId(SubTaskContext.getSubTaskId());
		entity.setMarkdownRequest(markdownRequest);
		this.updateById(entity);
		
		// 记日志
		this.subTaskLogService.save(LogLevel.INFO, "markdown请求参数准备完毕");
	}

	@Override
	public void updateMarkdownResponse(String markdownResponse) {
		LocalDateTime now = LocalDateTime.now();
		SubTaskRecord entity = new SubTaskRecord();
		entity.setSubTaskId(SubTaskContext.getSubTaskId());
		entity.setMarkdownResponse(markdownResponse);
		entity.setEndTime(now);
		this.updateById(entity);

		// 记日志
		this.subTaskLogService.save(LogLevel.INFO, "markdown响应成功");
	}

	@Override
	public void updateAIRequest(String aiRequest) {
		LocalDateTime now = LocalDateTime.now();
		SubTaskRecord entity = SubTaskRecord.builder()
				.subTaskId(SubTaskContext.getSubTaskId())
				.aiRequest(aiRequest)
				.endTime(now)
				.build();
		this.updateById(entity);

		// 记日志
		this.subTaskLogService.save(LogLevel.INFO, "AI请求参数准备完毕");
	}
	
	public void updateAIResponse(String aiResponse) {
		LocalDateTime now = LocalDateTime.now();
		SubTaskRecord entity = SubTaskRecord.builder()
				.subTaskId(SubTaskContext.getSubTaskId())
				.endTime(now)
				.aiResponse(aiResponse)
				.build();
		this.updateById(entity);
		
		// 记日志
		this.subTaskLogService.save(LogLevel.INFO, "AI响应成功");
	}
	
	@Override
	public void updateAIResult(ChatCompletionsVO chatCompletionsVO) {
		LocalDateTime now = LocalDateTime.now();
		Message responseMessage = chatCompletionsVO.getChoices().get(0).getMessage();
		SubTaskRecord entity = SubTaskRecord.builder()
				.subTaskId(SubTaskContext.getSubTaskId())
				.endTime(now)
				.aiContent(responseMessage.getContent())
				.aiReasoning(responseMessage.getReasoning_content())
				.aiTokenUsage(JSON.toJSONString(chatCompletionsVO.getUsage()))
				.status(TaskStatus.SUCCESS.name())
				.finishTime(now)
				.build();
		this.updateById(entity);
		
		// 记日志
		this.subTaskLogService.save(LogLevel.INFO, "AI结果保存成功");
		
		SubTaskContext.set(SubTaskContext.getTaskId(), SubTaskContext.getSubTaskId(), SubTaskContext.getUrl());
		// 主任务的所有子任务如果都成功了，更新主任务状态
		this.taskRecordService.succeedWhenSubtaskSucceed(SubTaskContext.getTaskId());
	}


}
