package com.my.college.auth.controller;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.my.college.auth.AuthConstant;
import com.my.college.auth.controller.dto.LoginDTO;
import com.my.college.controller.vo.StdResp;
import com.my.college.exception.BusinessException;
import com.my.college.mybatis.entity.SysUser;
import com.my.college.service.SysUserService;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;

/**
 * 授权
 */
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
public class AuthController {

	private final SysUserService sysUserService;

    
    /**
     * 登录
     * @param loginDTO
     * @return
     */
    @SaIgnore
    @PostMapping("/login")
    public StdResp<String> login(@RequestBody @Valid LoginDTO loginDTO) {
        String token = this.checkAndLogin(loginDTO);
        return StdResp.success(token);
    }
    
    /**
     * 获取用户信息
     * @return
     */
    @GetMapping("/info")
    public StdResp<SysUser> info() {
    	String userId = StpUtil.getLoginIdAsString();
		SysUser user = this.sysUserService.getById(userId);
    	return StdResp.success(user);
    }

    /**
     * 退出登录
     *
     * @return
     */
    @GetMapping("/logout")
    public StdResp<?> logout() {
        StpUtil.logout(StpUtil.getLoginId());
        return StdResp.success();
    }
    
    /**
     * 登录并返回token
     * @param loginDTO
     */
    private String checkAndLogin(LoginDTO loginDTO) {
    	SysUser user = this.sysUserService.getByUsernameAndPassword(loginDTO.getUsername(), loginDTO.getPassword());
    	BusinessException.when(user == null, "用户名或密码错误");
    	
        // stpUtil登录
        StpUtil.login(user.getUsername());
        
        // saSession中保存user
        StpUtil.getTokenSession().set(AuthConstant.TOKEN_SESSION_USER, user);
        
        return StpUtil.getTokenValue();
    }
    

}
