package com.my.college.forest.markdown.vo;

import java.math.BigDecimal;

import com.alibaba.fastjson2.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor @AllArgsConstructor @Builder @Data 
public class MarkdownVO {
	
	
	/**
	 * 是否成功
	 */
	@JSONField(name = "success", ordinal = 1)
	Boolean success;
	
	
	/**
	 * url
	 */
	@JSONField(name = "url", ordinal = 2)
	String url;


    /**
     * 内容
     */
    @JSONField(name = "markdown_content", ordinal = 3)
    String markdown_content;

    /**
     * 总耗时
     */
    @JSONField(name = "processing_time", ordinal = 4)
    BigDecimal processing_time;


}