<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.college.mybatis.mapper.SubTaskLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.college.mybatis.entity.SubTaskLog">
        <id column="log_id" property="logId" />
        <result column="sub_task_id" property="subTaskId" />
        <result column="type" property="type" />
        <result column="content" property="content" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        log_id, sub_task_id, type, content, create_time
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.college.controller.vo.sub_task_log.SubTaskLogPageVO">
		SELECT
			log_id, sub_task_id, type, content, create_time
		FROM
			sub_task_log AS t1
		<where>
        	1=1
	        <if test="logId != null and logId != ''">
	           	AND t1.log_id = #{logId}
            </if>
	        <if test="subTaskId != null and subTaskId != ''">
	           	AND t1.sub_task_id = #{subTaskId}
            </if>
	        <if test="type != null and type != ''">
	           	AND t1.type = #{type}
            </if>
	        <if test="content != null and content != ''">
	           	AND t1.content = #{content}
            </if>
	        <if test="createTime != null and createTime != ''">
	           	AND t1.create_time = #{createTime}
            </if>
        </where>
    </select>

</mapper>
