package com.my.college.util;

import java.io.FileInputStream;

import org.junit.jupiter.api.Test;

import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class OfficeUtilTest {

	String folder = "C:\\Users\\<USER>\\Desktop\\test\\";
	
	@Test
	void testId() {
		String str1 = IdUtil.fastSimpleUUID();
		String str2 = IdUtil.fastUUID();
		String str3 = IdUtil.getSnowflakeNextIdStr();
		log.info(str1);
		log.info(str2);
		log.info(str3);

		for (int i = 0; i < 300; i++) {
			System.out.println(IdUtil.getSnowflakeNextIdStr());
		}
	}
	
	// 方式1：通过文件路径转换
	@Test
	void testWord2MarkdownString() {
		String mdContent = OfficeUtil.word2Markdown(folder + "strategy.docx");
		log.info(mdContent);
	}

	// 方式2：通过输入流转换
	@Test
	void testWord2MarkdownInputStream() throws Exception {
		try (FileInputStream fis = new FileInputStream("D:/path/to/document.docx")) {
		    String mdContent = OfficeUtil.word2Markdown(fis);
		}
	}

	// 方式1：通过文件路径转换
	@Test
	void testExcel2CsvString() {
		String statusContent = OfficeUtil.excel2Csv(folder + "status.xlsx");
		log.info(statusContent);
		
		String cmdTplContent = OfficeUtil.excel2Csv(folder + "cmd_tpl.xlsx");
		log.info(cmdTplContent);

	}
	
	// 方式2：通过输入流转换
	@Test
	void testExcel2CsvInputStream() throws Exception {
		try (FileInputStream fis = new FileInputStream("D:/path/to/file.xlsx")) {
		    String csvContent = OfficeUtil.excel2Csv(fis);
		}
	}
	
}
