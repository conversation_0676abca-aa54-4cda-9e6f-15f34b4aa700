package com.my.college.controller.dto.address_book;

import java.io.Serializable;
import java.util.Map;

import javax.validation.ConstraintViolationException;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson2.JSON;
import com.my.college.exception.BusinessException;
import com.my.college.service.SchoolService;
import com.my.college.util.BeanValidators;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 修改_通讯录表
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Slf4j
public class AddressBookUploadDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 学校中文名称
     */
    @NotBlank(message="学校中文名称不能为空")
    @ExcelProperty("学校中文名称")
    private String schoolName;

    /**
     * 部门
     */
    @ExcelProperty("部门")
    private String department;

    /**
     * 联系人姓名
     */
    @ExcelProperty("联系人姓名")
    @NotBlank(message="联系人姓名不能为空")
    private String contactName;

    /**
     * 邮箱
     */
    @ExcelProperty("邮箱")
    @Pattern(regexp = "\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*", message = "邮箱格式不正确")
    private String email;

    /**
     * 职位
     */
    @ExcelProperty("职位")
    private String position;

    /**
     * 电话号码
     */
    @ExcelProperty("电话号码")
    private String phone;


	/**
	 * 校验是否有效
	 * @param i 
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public String validate(int i) {
		try {
			// jsr验证
			BeanValidators.validateWithException(this);
			
			// 验证学校名称是否存在
			SchoolService schoolService = SpringUtil.getBean(SchoolService.class);
			if (schoolService.getByChineseName(this.schoolName) == null) {
				BusinessException.by("学校名称不存在");
			}
			return StrUtil.EMPTY;
		} catch(ConstraintViolationException e) {
			Map<String, String> map = BeanUtil.copyProperties(this, Map.class);
			log.warn("【联系人excel】数据行校验失败，忽略该行. {}, err:{}", JSON.toJSONString(map), e.getMessage());
			return StrUtil.format("第{}行:{}", (i+1), BeanValidators.extractPropertyAndMessageAsList(e));
		} catch(Exception e) {
			return StrUtil.format("第{}行:{}", (i+1), e.getMessage());
		}
	}
}
