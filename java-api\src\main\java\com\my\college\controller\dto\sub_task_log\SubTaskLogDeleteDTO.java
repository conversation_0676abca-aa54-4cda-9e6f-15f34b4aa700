package com.my.college.controller.dto.sub_task_log;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotEmpty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 批量删除_子任务日志表
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SubTaskLogDeleteDTO 
						implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	* 主键数组
	*/
	@NotEmpty(message = "idList不能为空")
	private List<Integer> idList;
	
}
