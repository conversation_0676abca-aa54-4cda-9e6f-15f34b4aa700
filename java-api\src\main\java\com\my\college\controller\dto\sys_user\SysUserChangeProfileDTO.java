package com.my.college.controller.dto.sys_user;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改用户资料
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUserChangeProfileDTO 
				extends SysUserIdDTO
				implements Serializable {

    private static final long serialVersionUID = 1L;

    
    /**
     * 真实姓名
     */
	@NotBlank(message="realName不能为空")
    private String realName;

    /**
     * 手机号
     */
	@Pattern(regexp = "\\d{11}", message = "手机号必须是11位数字")
    private String mobile;

	/**
	 * 角色
	 */
	@NotBlank(message = "role不能为空")
	private String role;
	
}
