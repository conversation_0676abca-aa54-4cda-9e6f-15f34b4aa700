package com.my.college.controller.vo.dept;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 查看实体详情_部门表
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class DeptPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门
     */
    private String dept;

}
