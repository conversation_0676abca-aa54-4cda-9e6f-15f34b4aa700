package com.my.college.util;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;

import cn.hutool.core.collection.CollectionUtil;

/**
 * 分页排序工具类
 * @date 2024.06.05
 */
public class PageOrderUtil {

	/**
	 * 设置排序字段 (或默认)
	 * @param orders
	 * @param defaultSFunc
	 * @param cls 
	 */
	public static <T> void setOrderFieldsOrDefault(List<OrderItem> orders, SFunction<T, ?> defaultSFunc, Class<T> cls) {
		ColumnLambda<T> colLambda = new ColumnLambda<T>();
		defaultSFunc.getClass().getSimpleName();
		if (CollectionUtil.isNotEmpty(orders)) {
			for (OrderItem o : orders) {
				String fieldName = o.getColumn();
				String colName = colLambda.colName(fieldName, cls);
				o.setColumn(colName);
			}
		} else {
			String colName = colLambda.columnsToString(defaultSFunc);
			orders.add(new OrderItem(colName, false));
		}
	}
}
