package com.my.college.mybatis.mapper;

import com.my.college.controller.dto.address_book.AddressBookPageDTO;
import com.my.college.controller.vo.address_book.AddressBookPageVO;
import com.my.college.mybatis.entity.AddressBook;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 通讯录表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface AddressBookMapper extends BaseMapper<AddressBook> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<AddressBookPageVO> page(AddressBookPageDTO pageDTO);
	
}
