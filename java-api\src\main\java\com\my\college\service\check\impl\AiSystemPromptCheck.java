package com.my.college.service.check.impl;

import java.lang.reflect.Field;

import org.apache.commons.lang3.StringUtils;

import com.my.college.service.check.AbstractCheck;
import com.my.college.service.check.result.CheckResult;

/**
 * ai系统提示词
 * <AUTHOR>
 */
public class AiSystemPromptCheck extends AbstractCheck {

	
    @Override
    protected CheckResult doCheck(Object fieldValue, Field field, Object obj) {
        String val = (String) fieldValue;
        
        if (StringUtils.isBlank(val)) {
            return fail(this.fieldLabel + "不能为空");
        }
        
        return success();
    }
}
