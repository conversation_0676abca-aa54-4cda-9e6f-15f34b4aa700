package com.my.college.service;

import com.my.college.mybatis.entity.Province;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.college.controller.dto.province.ProvinceInsertDTO;
import com.my.college.controller.dto.province.ProvincePageDTO;
import com.my.college.controller.dto.province.ProvinceUpdateDTO;
import com.my.college.controller.dto.province.ProvinceDeleteDTO;
import com.my.college.controller.vo.province.ProvinceDetailVO;
import com.my.college.controller.vo.province.ProvincePageVO;

/**
 * 美国50个州州名 服务类
 *
 * <AUTHOR>
 * @date 2025-05-25
 */
public interface ProvinceService extends IService<Province> {

	/**
	 * 新增
	 */
	void insert(ProvinceInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(ProvinceUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	ProvinceDetailVO detail(String id);

	/**
	 * 分页
	 */
	Page<ProvincePageVO> page(ProvincePageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(ProvinceDeleteDTO deleteDTO);	

	/**
	 * 根据省份ID或中文名称查找省份
	 * @param value 省份ID或中文名称
	 * @return 匹配的省份，如果没有找到返回null
	 */
	Province findByIdOrName(String value);
}
