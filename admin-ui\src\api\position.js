import { reqGet, reqPut, reqDelete, reqPost } from './axiosFun';

// 分页
export const positionPage = (params) => { return reqGet("/position/page", params) };

// 获取全部职位列表
export const positionList = (params) => { return reqGet("/position/list", params) };

// 获取详情
export const positionDetail = (params) => { return reqGet("/position/" + params.position) };

// 创建职位
export const createPosition = (params) => {return reqPost("/position", params);};

// 更新职位
export const updatePosition = (params) => {return reqPut("/position", params);};

// 删除职位
export const deletePosition = (params) => { return reqDelete("/position", params) };
