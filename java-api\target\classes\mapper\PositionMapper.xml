<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.college.mybatis.mapper.PositionMapper">


	<!-- 分页 -->    
    <select id="page" resultType="com.my.college.controller.vo.position.PositionPageVO">
		SELECT
			position
		FROM
			position AS t1
		<where>
        	1=1
	        <if test="position != null and position != ''">
	           	AND t1.position like concat('%', #{position}, '%')
            </if>
        </where>
    </select>

</mapper>
