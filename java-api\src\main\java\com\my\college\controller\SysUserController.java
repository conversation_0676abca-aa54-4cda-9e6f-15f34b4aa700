package com.my.college.controller;

import java.util.List;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.college.controller.dto.sys_user.SysUserChangeMyPasswordDTO;
import com.my.college.controller.dto.sys_user.SysUserChangePasswordDTO;
import com.my.college.controller.dto.sys_user.SysUserDeleteDTO;
import com.my.college.controller.dto.sys_user.SysUserInsertDTO;
import com.my.college.controller.dto.sys_user.SysUserPageDTO;
import com.my.college.controller.dto.sys_user.SysUserUpdateDTO;
import com.my.college.controller.vo.StdResp;
import com.my.college.controller.vo.sys_user.SysUserPageVO;
import com.my.college.mybatis.entity.SysUser;
import com.my.college.service.SysUserService;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;

/**
 * 系统用户
 * 
 * @date 2024-05-20
 */
@RestController
@RequestMapping("/api/sys-user")
@RequiredArgsConstructor
public class SysUserController {

    private final SysUserService sysUserService;

    
    /**
     * 查询我的资料
     */
    @GetMapping(value = "profile")
    public StdResp<SysUser> profile() {
    	String userId = StpUtil.getLoginIdAsString();
		SysUser entity = this.sysUserService.getById(userId);
    	return StdResp.success(entity);
    }
    
    /**
     * 修改我的密码
     */
    @PutMapping("/change-my-password")
    public StdResp<?> changeMyPassword(@Valid @RequestBody SysUserChangeMyPasswordDTO dto) {
    	this.sysUserService.changeMyPassword(dto);
    	return StdResp.success();
    }
    
    /**
     * 修改指定人员密码
     */
    @PutMapping("/change-password")
    public StdResp<?> changePassword(@Valid @RequestBody SysUserChangePasswordDTO dto) {
    	this.sysUserService.changePassword(dto);
    	return StdResp.success();
    }

    /**
    * 新增
    */
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody SysUserInsertDTO insertDTO) {
    	this.sysUserService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @PutMapping("")
    public StdResp<?> update(@Valid @RequestBody SysUserUpdateDTO updateDTO) {
    	this.sysUserService.update(updateDTO);
    	return StdResp.success();
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<SysUserPageVO>> page(SysUserPageDTO pageDTO) {
        Page<SysUserPageVO> page = this.sysUserService.page(pageDTO);
        return StdResp.success(page);
    }
    
    
    /**
     * 查询全部用户
     */
    @GetMapping(value = "list")
    public StdResp<List<SysUser>> list() {
    	List<SysUser> list = this.sysUserService.list();
    	return StdResp.success(list);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody SysUserDeleteDTO deleteDTO) {
    	this.sysUserService.delete(deleteDTO);
		return StdResp.success();
    }
    
}
