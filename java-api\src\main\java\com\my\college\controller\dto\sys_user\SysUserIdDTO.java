package com.my.college.controller.dto.sys_user;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 用户id
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUserIdDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     * @mock U001
     */
	@NotNull(message="userId不能为空")
    private String userId;


}
