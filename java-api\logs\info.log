2025-07-29 10:29:08.538 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 10:29:08.544 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,ali<PERSON>-<PERSON>,ali<PERSON>-db,aliyun-forest
2025-07-29 10:29:09.416 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 10:29:09.417 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 10:29:09.417 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [<PERSON>] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 10:29:09.423 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 10:29:12.193 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 10:29:13.894 INFO [ main] com.my.college.App:61 - Started App in 6.314 seconds (JVM running for 7.574)
2025-07-29 10:29:13.907 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 11:01:03.249 ERROR [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 134248
2025-07-29 11:01:03.252 ERROR [http-nio-20000-exec-4] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 134426
2025-07-29 11:04:12.347 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 118592
2025-07-29 11:04:12.374 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 119049
2025-07-29 11:15:03.916 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 11:15:03.922 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-29 11:15:05.073 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 11:15:05.073 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 11:15:05.073 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 11:15:05.073 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 11:15:08.140 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 11:15:09.575 INFO [ main] com.my.college.App:61 - Started App in 6.704 seconds (JVM running for 8.35)
2025-07-29 11:15:09.586 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 11:16:31.339 ERROR [http-nio-20000-exec-8] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-29 11:16:31.339 ERROR [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-29 11:16:31.339 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-29 11:16:31.339 ERROR [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-29 11:18:18.620 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 190146
2025-07-29 11:21:46.065 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 198173
2025-07-29 11:21:46.066 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 170096
2025-07-29 11:26:10.527 ERROR [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 248162
2025-07-29 11:26:10.528 ERROR [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 264060
2025-07-29 12:52:55.525 ERROR [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 114204
2025-07-29 12:52:55.554 ERROR [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 126101
2025-07-29 12:55:41.191 ERROR [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 142294
2025-07-29 12:55:41.192 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 128987
2025-07-29 13:01:06.269 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 126225
2025-07-29 13:01:06.501 ERROR [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 172617
2025-07-29 13:02:18.959 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 69491
2025-07-29 13:02:18.987 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 69518
2025-07-29 13:03:28.685 ERROR [http-nio-20000-exec-1] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 62193
2025-07-29 13:03:28.685 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 62193
2025-07-29 13:06:16.471 ERROR [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 167277
2025-07-29 13:06:16.471 ERROR [http-nio-20000-exec-4] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 167428
2025-07-29 13:11:54.278 ERROR [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 109339
2025-07-29 13:11:54.278 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 109339
2025-07-29 13:17:34.572 ERROR [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 236537
2025-07-29 13:17:34.576 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 236543
2025-07-29 13:28:02.379 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 71282
2025-07-29 13:28:02.379 ERROR [http-nio-20000-exec-1] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 71439
2025-07-29 13:29:28.048 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 84980
2025-07-29 13:29:28.074 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 85147
2025-07-29 13:31:33.957 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 90998
2025-07-29 13:31:33.984 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 91034
2025-07-29 15:55:27.500 ERROR [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 112107
2025-07-29 15:55:27.504 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 112259
2025-07-29 16:22:29.134 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 16:22:29.141 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-29 16:22:30.499 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 16:22:30.500 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 16:22:30.500 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 16:22:30.500 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 16:22:33.523 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 16:22:34.856 INFO [ main] com.my.college.App:61 - Started App in 6.948 seconds (JVM running for 13.997)
2025-07-29 16:22:34.866 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 16:22:54.902 ERROR [http-nio-20000-exec-1] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/page, Err-detail:接口401
2025-07-29 16:24:25.402 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 73487
2025-07-29 16:24:25.429 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 73525
2025-07-29 16:30:37.383 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 16:30:37.389 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-29 16:30:38.343 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 16:30:38.343 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 16:30:38.343 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 16:30:38.343 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 16:30:41.168 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 16:30:42.603 INFO [ main] com.my.college.App:61 - Started App in 6.128 seconds (JVM running for 7.34)
2025-07-29 16:30:42.613 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 16:42:55.045 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/page, Err-detail:接口401
2025-07-29 16:50:57.318 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 73190
2025-07-29 16:50:57.321 ERROR [http-nio-20000-exec-6] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 73198
2025-07-29 16:53:16.121 ERROR [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 127892
2025-07-29 16:53:16.124 ERROR [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 127874
2025-07-29 16:53:17.430 ERROR [http-nio-20000-exec-2] com.my.college.exception.ExceptionAspect:172 - Err-uri:/api/title/page, Err-detail:
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
### The error may exist in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\mapper\TitleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM title AS t1 WHERE 1 = 1
### Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
### The error may exist in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\mapper\TitleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM title AS t1 WHERE 1 = 1
### Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:235)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy83.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForIPage(MybatisMapperMethod.java:121)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:85)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy113.page(Unknown Source)
	at com.my.college.service.impl.TitleServiceImpl.page(TitleServiceImpl.java:50)
	at com.my.college.service.impl.TitleServiceImpl$$FastClassBySpringCGLIB$$d95511a6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:685)
	at com.my.college.service.impl.TitleServiceImpl$$EnhancerBySpringCGLIB$$5a5351ac.page(<generated>)
	at com.my.college.controller.TitleController.page(TitleController.java:66)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:888)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.dev33.satoken.filter.SaPathCheckFilterForServlet.doFilter(SaPathCheckFilterForServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:367)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:860)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1598)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Unknown Source)
Caused by: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.GeneratedMethodAccessor103.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy90.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.GeneratedMethodAccessor106.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy88.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor.willDoQuery(PaginationInnerInterceptor.java:141)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:75)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy87.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 70 common frames omitted
2025-07-29 16:53:27.087 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:172 - Err-uri:/api/title/page, Err-detail:
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
### The error may exist in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\mapper\TitleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM title AS t1 WHERE 1 = 1
### Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
### The error may exist in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\mapper\TitleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM title AS t1 WHERE 1 = 1
### Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:235)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy83.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForIPage(MybatisMapperMethod.java:121)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:85)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy113.page(Unknown Source)
	at com.my.college.service.impl.TitleServiceImpl.page(TitleServiceImpl.java:50)
	at com.my.college.service.impl.TitleServiceImpl$$FastClassBySpringCGLIB$$d95511a6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:685)
	at com.my.college.service.impl.TitleServiceImpl$$EnhancerBySpringCGLIB$$5a5351ac.page(<generated>)
	at com.my.college.controller.TitleController.page(TitleController.java:66)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:888)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.dev33.satoken.filter.SaPathCheckFilterForServlet.doFilter(SaPathCheckFilterForServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:367)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:860)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1598)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Unknown Source)
Caused by: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.GeneratedMethodAccessor103.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy90.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.GeneratedMethodAccessor106.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy88.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor.willDoQuery(PaginationInnerInterceptor.java:141)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:75)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy87.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 70 common frames omitted
2025-07-29 16:54:40.637 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:172 - Err-uri:/api/title, Err-detail:
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/TitleMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.TitleMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO title  ( title )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
; Duplicate entry 'aa' for key 'title.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/TitleMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.TitleMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO title  ( title )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
; Duplicate entry 'aa' for key 'title.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:243)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy83.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy113.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.save(IService.java:63)
	at com.my.college.service.impl.TitleServiceImpl.insert(TitleServiceImpl.java:32)
	at com.my.college.service.impl.TitleServiceImpl$$FastClassBySpringCGLIB$$d95511a6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:366)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:99)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at com.my.college.service.impl.TitleServiceImpl$$EnhancerBySpringCGLIB$$5a5351ac.insert(<generated>)
	at com.my.college.controller.TitleController.insert(TitleController.java:39)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:888)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.dev33.satoken.filter.SaPathCheckFilterForServlet.doFilter(SaPathCheckFilterForServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:367)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:860)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1598)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Unknown Source)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.GeneratedMethodAccessor103.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy90.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy88.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy87.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 77 common frames omitted
2025-07-29 16:55:43.145 ERROR [http-nio-20000-exec-10] com.my.college.exception.ExceptionAspect:172 - Err-uri:/api/dept, Err-detail:
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/DeptMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.DeptMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO dept  ( dept )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
; Duplicate entry 'd1' for key 'dept.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/DeptMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.DeptMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO dept  ( dept )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
; Duplicate entry 'd1' for key 'dept.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:243)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy83.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy99.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.save(IService.java:63)
	at com.my.college.service.impl.DeptServiceImpl.insert(DeptServiceImpl.java:32)
	at com.my.college.service.impl.DeptServiceImpl$$FastClassBySpringCGLIB$$980fc527.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:366)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:99)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at com.my.college.service.impl.DeptServiceImpl$$EnhancerBySpringCGLIB$$3cab24cf.insert(<generated>)
	at com.my.college.controller.DeptController.insert(DeptController.java:39)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:888)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.dev33.satoken.filter.SaPathCheckFilterForServlet.doFilter(SaPathCheckFilterForServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:367)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:860)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1598)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Unknown Source)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.GeneratedMethodAccessor103.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy90.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy88.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy87.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 77 common frames omitted
2025-07-29 17:04:12.157 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 17:04:12.162 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-29 17:04:13.191 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 17:04:13.191 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 17:04:13.191 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 17:04:13.191 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 17:04:17.066 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 17:04:18.829 INFO [ main] com.my.college.App:61 - Started App in 7.594 seconds (JVM running for 8.833)
2025-07-29 17:04:18.841 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 17:06:01.753 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/page, Err-detail:接口401
2025-07-29 17:06:01.753 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/page, Err-detail:接口401
2025-07-29 17:06:01.753 ERROR [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/page, Err-detail:接口401
2025-07-29 17:06:08.028 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 110384
2025-07-29 17:06:18.796 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:172 - Err-uri:/api/title, Err-detail:
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/TitleMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.TitleMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO title  ( title )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
; Duplicate entry 'aa' for key 'title.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/TitleMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.TitleMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO title  ( title )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
; Duplicate entry 'aa' for key 'title.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:243)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy83.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy113.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.save(IService.java:63)
	at com.my.college.service.impl.TitleServiceImpl.insert(TitleServiceImpl.java:32)
	at com.my.college.service.impl.TitleServiceImpl$$FastClassBySpringCGLIB$$d95511a6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:366)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:99)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at com.my.college.service.impl.TitleServiceImpl$$EnhancerBySpringCGLIB$$d53de090.insert(<generated>)
	at com.my.college.controller.TitleController.insert(TitleController.java:39)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:888)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.dev33.satoken.filter.SaPathCheckFilterForServlet.doFilter(SaPathCheckFilterForServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:367)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:860)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1598)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Unknown Source)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy90.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy88.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy87.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 77 common frames omitted
2025-07-29 17:06:37.077 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 17:06:37.083 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-29 17:06:38.053 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 17:06:38.053 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 17:06:38.054 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 17:06:38.054 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 17:06:41.882 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 17:06:43.463 INFO [ main] com.my.college.App:61 - Started App in 7.311 seconds (JVM running for 8.506)
2025-07-29 17:06:43.473 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 17:59:05.981 ERROR [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-29 17:59:05.981 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-29 17:59:05.981 ERROR [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-29 17:59:05.981 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-29 17:59:22.279 ERROR [http-nio-20000-exec-8] com.my.college.exception.ExceptionAspect:84 - Err-uri:/api/title, Err-detail:标题 [aa] 已存在
2025-07-29 17:59:22.507 ERROR [http-nio-20000-exec-9] com.my.college.exception.ExceptionAspect:84 - Err-uri:/api/title, Err-detail:标题 [aa] 已存在
2025-07-29 18:00:32.131 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 18:00:32.137 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-29 18:00:33.032 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 18:00:33.032 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 18:00:33.032 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 18:00:33.032 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 18:00:36.302 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 18:00:37.720 INFO [ main] com.my.college.App:61 - Started App in 6.544 seconds (JVM running for 13.536)
2025-07-29 18:00:37.732 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 18:00:38.451 ERROR [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/page, Err-detail:接口401
2025-07-29 18:00:38.451 ERROR [http-nio-20000-exec-2] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/page, Err-detail:接口401
2025-07-29 18:00:38.451 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/page, Err-detail:接口401
2025-07-29 18:06:01.766 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 129991
2025-07-29 18:06:01.766 ERROR [http-nio-20000-exec-6] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, jdbcUrl : ***********************************************************************************************************************************************mezone=Asia/Shanghai&rewriteBatchedStatements=true, lastPacketReceivedIdleMillis : 130002
2025-07-29 18:08:07.807 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 18:08:07.813 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-29 18:08:08.678 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 18:08:08.678 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 18:08:08.678 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 18:08:08.679 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 18:08:11.431 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 18:08:12.760 INFO [ main] com.my.college.App:61 - Started App in 5.911 seconds (JVM running for 7.103)
2025-07-29 18:08:12.770 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 18:08:16.769 ERROR [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/page, Err-detail:接口401
2025-07-29 18:08:16.769 ERROR [http-nio-20000-exec-1] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/page, Err-detail:接口401
2025-07-29 18:08:16.769 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/page, Err-detail:接口401
