package com.my.college.mybatis.mapper;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.college.controller.dto.task_record.TaskRecordPageDTO;
import com.my.college.controller.vo.task_record.TaskRecordPageVO;
import com.my.college.mybatis.entity.TaskRecord;

/**
 * 任务记录表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-05-03
 */
public interface TaskRecordMapper extends BaseMapper<TaskRecord> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<TaskRecordPageVO> page(TaskRecordPageDTO pageDTO);

	/**
	 * 如果子任务全部完成，则主任务完成
	 * @param taskId 
	 */
	boolean succeedWhenSubtaskSucceed(@Param("taskId")String taskId);
	
}
