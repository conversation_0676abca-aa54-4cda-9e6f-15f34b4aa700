package com.my.college.forest.markdown;

import com.dtflys.forest.annotation.Address;
import com.dtflys.forest.annotation.BodyType;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.callback.OnError;
import com.my.college.forest.markdown.address.MarkdownAddress;
import com.my.college.forest.markdown.dto.MarkdownDTO;
import com.my.college.forest.markdown.vo.MarkdownVO;

/**
 * markdown接口
 * 
 * <AUTHOR>
 */
@Address(source = MarkdownAddress.class)
public interface MarkdownClient {

    
	/**
	 * 获取markdown内容
	 */
	@Post(url = "/markdown", 
		readTimeout = 60 * 1000, 
		connectTimeout =  60 * 1000)
	@BodyType("json")
	MarkdownVO markdown(@JSONBody MarkdownDTO markdownDTO, OnError onError);
	
    
} 