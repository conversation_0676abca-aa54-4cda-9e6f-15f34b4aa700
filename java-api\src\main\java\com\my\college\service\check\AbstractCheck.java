package com.my.college.service.check;

import java.lang.reflect.Field;

import com.my.college.service.check.annotation.Check;
import com.my.college.service.check.result.CheckResult;

/**
 * 字段检测抽象父类
 * 提供基础的字段名称和标签获取逻辑
 * 
 * <AUTHOR>
 */
public abstract class AbstractCheck 
								implements ICheck {

	private static final String VALID = "有效";
	
	
    /** 字段名称  */
    protected String fieldName;
    
    /** 字段标签 */
    protected String fieldLabel;
    
    
    @Override
    public final CheckResult check(Object fieldValue, Field field, Object obj) {
        // 初始化字段名称和标签
        this.fieldName = getFieldName(field);
        this.fieldLabel = getFieldLabel(field);
        
        // 调用子类的具体检测逻辑
        return doCheck(fieldValue, field, obj);
    }
    
    /**
     * 子类实现具体的检测逻辑
     * @param fieldValue 字段值
     * @param field 字段反射对象
     * @param obj 包含该字段的对象
     * @return 检测结果
     */
    protected abstract CheckResult doCheck(Object fieldValue, Field field, Object obj);
    
    /**
     * 获取字段名称
     * @param field 字段反射对象
     * @return 字段名称
     */
    @Override
    public String getFieldName(Field field) {
        return field.getName();
    }
    
    /**
     * 从@Check注解的label属性获取
     * @param field 字段反射对象
     * @return 字段的中文标签
     */
    @Override
    public String getFieldLabel(Field field) {
        Check checkAnnotation = field.getAnnotation(Check.class);
        return checkAnnotation.label();
    }
    
    /**
     * 检测成功
     * @param message 检测消息
     * @return 检测结果
     */
    protected CheckResult success(String message) {
        return new CheckResult(this.fieldName, this.fieldLabel, Boolean.TRUE, message);
    }
    
    /**
     * 检测成功
     * @param message 检测消息
     * @return 检测结果
     */
    protected CheckResult success() {
    	return new CheckResult(this.fieldName, this.fieldLabel, Boolean.TRUE, VALID);
    }
    
    /**
     * 检测失败
     * @param message 检测消息
     * @return 检测结果
     */
    protected CheckResult fail(String message) {
    	return new CheckResult(this.fieldName, this.fieldLabel, Boolean.FALSE, message);
    }
} 