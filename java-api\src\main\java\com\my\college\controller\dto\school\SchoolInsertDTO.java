package com.my.college.controller.dto.school;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_学校表
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SchoolInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 中文名称
     */
	@NotNull(message="chineseName不能为空")
    private String chineseName;

    /**
     * 英文名称
     */
    private String englishName;

    /**
     * 网址
     */
	@NotNull(message="website不能为空")
    private String website;

    /**
     * 备注
     */
    private String remark;

}
