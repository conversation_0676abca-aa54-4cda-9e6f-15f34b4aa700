package com.my.college.mybatis.mapper;

import com.my.college.controller.dto.sub_task_log.SubTaskLogPageDTO;
import com.my.college.controller.vo.sub_task_log.SubTaskLogPageVO;
import com.my.college.mybatis.entity.SubTaskLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 子任务日志表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
public interface SubTaskLogMapper extends BaseMapper<SubTaskLog> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<SubTaskLogPageVO> page(SubTaskLogPageDTO pageDTO);
	
}
