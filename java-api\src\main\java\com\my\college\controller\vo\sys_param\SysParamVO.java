package com.my.college.controller.vo.sys_param;

import java.io.Serializable;
import java.lang.reflect.Field;

import com.my.college.service.check.ICheck;
import com.my.college.service.check.annotation.Check;
import com.my.college.service.check.impl.AiApiKeyCheck;
import com.my.college.service.check.impl.AiModelCheck;
import com.my.college.service.check.impl.AiSystemPromptCheck;
import com.my.college.service.check.impl.AiTimeoutCheck;
import com.my.college.service.check.impl.AiUserPromptCheck;
import com.my.college.service.check.impl.GoogleApiKeyCheck;
import com.my.college.service.check.impl.GoogleEngineIDCheck;
import com.my.college.service.check.impl.GoogleKeywordCheck;
import com.my.college.service.check.impl.MarkdownTimeoutCheck;
import com.my.college.service.check.result.CheckResult;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 数据库配置的业务参数
 *
 * <AUTHOR>
 * @date 2025-05-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysParamVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * AI模型
     */
    @Check(chkClass = AiModelCheck.class, label = "AI模型")
    private String aiModel;

    /**
     * AI系统提示词
     */
    @Check(chkClass = AiSystemPromptCheck.class, label = "AI系统提示词")
    private String aiSystemPrompt;
    
    /**
     * AI用户提示词
     */
    @Check(chkClass = AiUserPromptCheck.class, label = "AI用户提示词")
    private String aiUserPrompt;

    /**
     * AI接口秘钥
     */
    @Check(chkClass = AiApiKeyCheck.class, label = "AI密钥")
    private String aiApiKey;
    
    /**
     * AI接口超时
     */
    @Check(chkClass = AiTimeoutCheck.class, label = "AI接口超时")
    private String aiTimeout;
    
    
    /**
     * 谷歌搜索关键词
     */
    @Check(chkClass = GoogleKeywordCheck.class, label = "谷歌搜索关键词")
    private String googleKeyword;
    
    /**
     * 谷歌搜索引擎API密钥
     */
    @Check(chkClass = GoogleApiKeyCheck.class, label = "谷歌搜索引擎API密钥")
    private String googleApiKey;
    
    /**
     * 谷歌搜索引擎ID
     */
    @Check(chkClass = GoogleEngineIDCheck.class, label = "谷歌搜索引擎ID")
    private String googleEngineID;
    
    /**
     * markdown超时
     */
    @Check(chkClass = MarkdownTimeoutCheck.class, label = "markdown接口超时")
    private String markdownTimeout;
    
    
    /**
	 * 检测对象中标注了@Check注解的字段
	 * @param obj 要检测的对象
	 * @return 检测结果Map，key为字段名，value为检测结果
	 */
	public SysParamCheckVO check() {
		SysParamCheckVO resultMap = new SysParamCheckVO();
		Class<?> clazz = this.getClass();
		Field[] fields = clazz.getDeclaredFields();
		
		for (Field field : fields) {
			Check checkAnnotation = field.getAnnotation(Check.class);
			if (checkAnnotation != null) {
				try {
					// 设置字段可访问
					field.setAccessible(true);
					
					// 获取字段值
					Object fieldValue = field.get(this);
					
					// 获取检测器类
					Class<? extends ICheck> checkClass = checkAnnotation.chkClass();
					ICheck checker = checkClass.newInstance();
					
					// 执行检测
					CheckResult result = checker.check(fieldValue, field, this);
					resultMap.put(field.getName(), result);
					
				} catch (Exception e) {
					// 如果检测过程中出现异常，记录错误结果
					CheckResult errorResult = new CheckResult(
						field.getName(), 
						field.getName(), 
						false, 
						e.getMessage()
					);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   
					resultMap.put(field.getName(), errorResult);
				}
			}
		}
		
		return resultMap;
	}
}
