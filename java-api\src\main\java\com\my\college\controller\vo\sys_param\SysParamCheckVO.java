package com.my.college.controller.vo.sys_param;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import com.my.college.service.check.result.CheckResult;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 业务参数每个字段的检测结果
 *
 * <AUTHOR>
 * @date 2025-05-01
 */
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysParamCheckVO
					extends HashMap<String, CheckResult> 
					implements Serializable {

    private static final long serialVersionUID = 1L;
 

    /*
     * ...
     * 因为继承自HashMap， 有些字段这里看不到，但是能get获取到
     * 
     * 字段1 -> CheckResult
     * 字段1 -> CheckResult
     * ...
     */
    
    
    /**
     * 仅返回无效的字段
     */
    public List<CheckResult> invalidItems() {
    	return this.values().stream()
	    	.filter(t -> !t.getValid())
	    	.collect(Collectors.toList());
    }
    
} 