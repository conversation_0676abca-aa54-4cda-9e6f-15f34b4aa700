package com.my.college.forest.siliconflow.onerrror;

import java.net.SocketTimeoutException;

import com.dtflys.forest.callback.OnError;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 接口异常处理
 */
@Slf4j
public class SiliconflowOnError implements OnError {

	@Getter
	private String errMsg;
	

	@SuppressWarnings("rawtypes")
	@Override
	public void onError(ForestRuntimeException e, ForestRequest req, ForestResponse response) {
	    Throwable cause = e.getCause();
	    if (cause instanceof SocketTimeoutException) {
	        this.errMsg = "[接口超时]";
	        return;
	    }
	    if (401 == response.getStatusCode()) {
			this.errMsg = "[密钥无效]";
			return;
		}
		this.errMsg = e.getMessage();
		log.error("Siliconflow接口异常：{}", errMsg);
	}
	
	/**
	 * 是否报错
	 * @return
	 */
	public boolean isError() {
		return StrUtil.isNotBlank(errMsg);
	}

}
