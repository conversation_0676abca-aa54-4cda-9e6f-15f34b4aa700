package com.my.college.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.my.college.controller.dto.address_book.AddressBookDeleteDTO;
import com.my.college.controller.dto.address_book.AddressBookInsertDTO;
import com.my.college.controller.dto.address_book.AddressBookPageDTO;
import com.my.college.controller.dto.address_book.AddressBookUpdateDTO;
import com.my.college.controller.dto.address_book.AddressBookUploadDTO;
import com.my.college.controller.vo.address_book.AddressBookDetailVO;
import com.my.college.controller.vo.address_book.AddressBookPageVO;
import com.my.college.mybatis.entity.AddressBook;
import com.my.college.mybatis.entity.School;
import com.my.college.mybatis.entity.TaskRecord;
import com.my.college.mybatis.mapper.AddressBookMapper;
import com.my.college.service.AddressBookService;
import com.my.college.service.SchoolService;
import com.my.college.service.TaskRecordService;
import com.my.college.task.TaskContext;
import com.my.college.task.subtask.SubTaskContext;
import com.my.college.util.ColumnLambda;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 通讯录表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AddressBookServiceImpl extends ServiceImpl<AddressBookMapper, AddressBook> implements AddressBookService {
	
	private final TaskRecordService taskRecordService;
	

	@Transactional
	@Override
	public void insert(AddressBookInsertDTO insertDTO) {
		AddressBook entity = BeanUtil.copyProperties(insertDTO, AddressBook.class);
		entity.setUpdateTime(LocalDateTime.now());
		entity.setManualFlag(Boolean.TRUE);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(AddressBookUpdateDTO updateDTO) {
		AddressBook entity = BeanUtil.copyProperties(updateDTO, AddressBook.class);
		entity.setUpdateTime(LocalDateTime.now());
		entity.setManualFlag(Boolean.TRUE);
		this.baseMapper.updateById(entity);
	}

	@Override
	public AddressBookDetailVO detail(Integer id) {
		AddressBook entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, AddressBookDetailVO.class);
	}

	@Override
	public Page<AddressBookPageVO> page(AddressBookPageDTO pageDTO) {
		List<OrderItem> orders = pageDTO.getOrders();
		if (CollectionUtil.isEmpty(orders)) {
			orders.add(OrderItem.desc(new ColumnLambda<AddressBook>().columnsToString(AddressBook::getId)));
		}
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(AddressBookDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}

	@Override
	public Integer countBySchoolId(List<Integer> schoolIdList) {
		Wrapper<AddressBook> wrapper = Wrappers.lambdaQuery(AddressBook.class)
				.in(AddressBook::getSchoolId, schoolIdList);
		return this.count(wrapper);
	}

	@Transactional
	@Override
	public void deleteBySchoolId(List<Integer> schoolIdList) {
		Wrapper<AddressBook> wrapper = Wrappers.lambdaQuery(AddressBook.class)
				.in(AddressBook::getSchoolId, schoolIdList);
		this.baseMapper.delete(wrapper);
	}

	@Transactional
	@Override
	public void batchInsert(Set<AddressBookUploadDTO> validList) {
		if (CollectionUtil.isEmpty(validList)) {
			return;
		}
		
		SchoolService schoolService = SpringUtil.getBean(SchoolService.class);
		int insertCount = 0;
		int updateCount = 0;
		
		for (AddressBookUploadDTO uploadDTO : validList) {
			Integer schoolId = TaskContext.getSchoolId();
			// 根据学校名称查询学校
			if (schoolId == null) {
				School school = schoolService.getByChineseName(uploadDTO.getSchoolName());
				schoolId = school.getSchoolId();
			}
			// 根据学校ID和联系人姓名查询是否存在
			AddressBook entity = this.getBySchoolIdAndContactName(schoolId, uploadDTO.getContactName());
			
			if (entity != null) {
				// 联系人存在，更新联系人信息
				entity.setDepartment(uploadDTO.getDepartment());
				entity.setEmail(uploadDTO.getEmail());
				entity.setPosition(uploadDTO.getPosition());
				entity.setPhone(uploadDTO.getPhone());
				entity.setManualFlag(false); // 标记为非手动添加
				entity.setUpdateTime(LocalDateTime.now());
				this.updateById(entity);
				updateCount++;
			} else {
				// 联系人不存在，新增联系人
				AddressBook newContact = new AddressBook();
				newContact.setSchoolId(schoolId);
				newContact.setDepartment(uploadDTO.getDepartment());
				newContact.setContactName(uploadDTO.getContactName());
				newContact.setEmail(uploadDTO.getEmail());
				newContact.setPosition(uploadDTO.getPosition());
				newContact.setPhone(uploadDTO.getPhone());
				newContact.setManualFlag(false); // 标记为非手动添加
				newContact.setUpdateTime(LocalDateTime.now());
				this.save(newContact);
				insertCount++;
			}
		}
		
		log.info("批量处理通讯录联系人数据成功，新增{}条，更新{}条", insertCount, updateCount);
	}

	@Override
	public AddressBook getBySchoolIdAndContactName(Integer schoolId, String contactName) {
		if (schoolId == null || StrUtil.isBlank(contactName)) {
			return null;
		}
		
		LambdaQueryWrapper<AddressBook> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(AddressBook::getSchoolId, schoolId)
				   .eq(AddressBook::getContactName, contactName);
		return this.getOne(queryWrapper);
	}

	@Override
	public void batchInsert(String aiContent) {
		String taskId = SubTaskContext.getTaskId();
		String url = SubTaskContext.getUrl();
		TaskRecord taskRecord = this.taskRecordService.getById(taskId);
		Integer schoolId = taskRecord.getSchoolId();
		// 批量新增
		LocalDateTime now = LocalDateTime.now();
		List<AddressBook> entityList = JSONArray.parseArray(aiContent, AddressBook.class);
		entityList.stream().forEach(t -> {
			t.setSchoolId(schoolId);
			t.setUpdateTime(now);
			t.setManualFlag(Boolean.FALSE);
			t.setUrl(url);
		});
		this.saveBatch(entityList);
	}
}
