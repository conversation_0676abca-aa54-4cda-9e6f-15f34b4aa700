package com.my.college.controller.dto.sys_user;

import java.io.Serializable;

import com.alibaba.fastjson2.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.college.controller.vo.sys_user.SysUserPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_用户信息表
 *
 * <AUTHOR>
 * @date 2025-05-21
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUserPageDTO 
						extends PageDTO<SysUserPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

	
	/**
	 * 是否管理员
	 */
    private Boolean adminFlag;;
    
    /**
     * 备注
     */
    private String remark;

}
