package com.my.college.controller.dto.address_book;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_通讯录表
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class AddressBookInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 学校ID
     */
	@NotNull(message="schoolId不能为空")
    private Integer schoolId;

    /**
     * 部门
     */
    private String department;

    /**
     * 联系人姓名
     */
	@NotBlank(message="contactName不能为空")
    private String contactName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 职位
     */
    private String position;

    /**
     * 电话号码
     */
    private String phone;

}
