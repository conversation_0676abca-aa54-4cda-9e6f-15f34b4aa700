<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.college.mybatis.mapper.AddressBookMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.college.mybatis.entity.AddressBook">
        <id column="id" property="id" />
        <result column="school_id" property="schoolId" />
        <result column="department" property="department" />
        <result column="contact_name" property="contactName" />
        <result column="email" property="email" />
        <result column="position" property="position" />
        <result column="phone" property="phone" />
        <result column="url" property="url" />
        <result column="update_time" property="updateTime" />
        <result column="manual_flag" property="manualFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, school_id, department, contact_name, email, position, phone, url, update_time, manual_flag
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.college.controller.vo.address_book.AddressBookPageVO">
		SELECT
			t1.id,
			t1.school_id,
			tSchool.chinese_name as schoolName, 
			department,
			contact_name,
			email,
			position,
			phone,
			url, 
			t1.update_time,
			manual_flag 
		FROM
			address_book AS t1
			LEFT JOIN school tSchool ON t1.school_id = tSchool.school_id
		<where>
        	1=1
	        <if test="id != null and id != ''">
	           	AND t1.id = #{id}
            </if>
	        <if test="schoolId != null and schoolId != ''">
	           	AND t1.school_id = #{schoolId}
            </if>
	        <if test="department != null and department != ''">
	           	AND t1.department = #{department}
            </if>
	        <if test="contactName != null and contactName != ''">
	           	AND t1.contact_name = #{contactName}
            </if>
	        <if test="email != null and email != ''">
	           	AND t1.email = #{email}
            </if>
	        <if test="position != null and position != ''">
	           	AND t1.position = #{position}
            </if>
	        <if test="phone != null and phone != ''">
	           	AND t1.phone = #{phone}
            </if>
	        <if test="updateTime != null and updateTime != ''">
	           	AND t1.update_time = #{updateTime}
            </if>
	        <if test="manualFlag != null and manualFlag != ''">
	           	AND t1.manual_flag = #{manualFlag}
            </if>
        </where>
    </select>

</mapper>
