package com.my.college.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

import org.apache.poi.xwpf.usermodel.IBodyElement;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;

import lombok.extern.slf4j.Slf4j;

/**
 * Office文件工具类
 * 提供Word文档与Markdown互相转换的功能
 */
@Slf4j
public class OfficeUtil {

    /**
     * 将Word文档(docx)转换为Markdown格式
     *
     * @param filePath Word文档路径
     * @return Markdown格式的字符串
     */
    public static String word2Markdown(String filePath) {
        try (InputStream is = new FileInputStream(new File(filePath))) {
            return word2Markdown(is);
        } catch (IOException e) {
            log.error("转换Word到Markdown失败", e);
            throw new RuntimeException("转换Word到Markdown失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将Word文档(docx)输入流转换为Markdown格式
     *
     * @param inputStream Word文档输入流
     * @return Markdown格式的字符串
     */
    public static String word2Markdown(InputStream inputStream) {
        try {
            XWPFDocument document = new XWPFDocument(inputStream);
            StringBuilder md = new StringBuilder();
            
            // 处理文档内容
            processDocument(document, md);
            
            return md.toString();
        } catch (IOException e) {
            log.error("转换Word到Markdown失败", e);
            throw new RuntimeException("转换Word到Markdown失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 处理Word文档内容
     *
     * @param document Word文档对象
     * @param md Markdown构建器
     */
    private static void processDocument(XWPFDocument document, StringBuilder md) {
        List<IBodyElement> bodyElements = document.getBodyElements();
        for (IBodyElement bodyElement : bodyElements) {
            if (bodyElement instanceof XWPFParagraph) {
                processParagraph((XWPFParagraph) bodyElement, md);
            } else if (bodyElement instanceof XWPFTable) {
                processTable((XWPFTable) bodyElement, md);
            }
        }
    }
    
    /**
     * 处理段落
     *
     * @param paragraph 段落对象
     * @param md Markdown构建器
     */
    private static void processParagraph(XWPFParagraph paragraph, StringBuilder md) {
        // 获取段落文本
        String text = paragraph.getText();
        if (text == null || text.trim().isEmpty()) {
            md.append("\n");
            return;
        }
        
        // 处理标题
        if (paragraph.getStyleID() != null && paragraph.getStyleID().startsWith("Heading")) {
            try {
                int headingLevel = Integer.parseInt(paragraph.getStyleID().substring("Heading".length()));
                for (int i = 0; i < headingLevel; i++) {
                    md.append("#");
                }
                md.append(" ").append(text).append("\n\n");
                return;
            } catch (NumberFormatException e) {
                // 如果解析标题级别失败，按普通段落处理
            }
        }
         
        // 处理列表
        if (paragraph.getNumID() != null) {
            md.append("- ").append(text).append("\n");
            return;
        }
        
        // 处理普通段落
        md.append(text).append("\n\n");
    }
    
    /**
     * 处理表格
     *
     * @param table 表格对象
     * @param md Markdown构建器
     */
    private static void processTable(XWPFTable table, StringBuilder md) {
        List<XWPFTableRow> rows = table.getRows();
        if (rows.isEmpty()) {
            return;
        }
        
        // 处理表头
        XWPFTableRow headerRow = rows.get(0);
        List<XWPFTableCell> headerCells = headerRow.getTableCells();
        for (XWPFTableCell cell : headerCells) {
            md.append("| ").append(cell.getText().trim()).append(" ");
        }
        md.append("|\n");
        
        // 添加表格分隔行
        for (int i = 0; i < headerCells.size(); i++) {
            md.append("| --- ");
        }
        md.append("|\n");
        
        // 处理表格数据行
        for (int i = 1; i < rows.size(); i++) {
            XWPFTableRow row = rows.get(i);
            List<XWPFTableCell> cells = row.getTableCells();
            for (XWPFTableCell cell : cells) {
                md.append("| ").append(cell.getText().trim()).append(" ");
            }
            md.append("|\n");
        }
        
        md.append("\n");
    }
    
    /**
     * 将Excel文件(xlsx)转换为CSV格式
     *
     * @param filePath Excel文件路径
     * @return CSV格式的字符串
     */
    public static String excel2Csv(String filePath) {
        try (InputStream is = new FileInputStream(new File(filePath))) {
            return excel2Csv(is);
        } catch (IOException e) {
            log.error("转换Excel到CSV失败", e);
            throw new RuntimeException("转换Excel到CSV失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将Excel文件(xlsx)输入流转换为CSV格式
     *
     * @param inputStream Excel文件输入流
     * @return CSV格式的字符串
     */
    public static String excel2Csv(InputStream inputStream) {
        try {
            // 使用StringBuilder收集CSV内容
            StringBuilder csvContent = new StringBuilder();
            
            // 使用EasyExcel读取Excel数据，并处理合并单元格
            // 先读取所有数据到内存中
            List<Map<Integer, Object>> allData = new ArrayList<>();
            EasyExcel.read(inputStream, new PageReadListener<Map<Integer, Object>>(dataList -> {
                allData.addAll(dataList);
            })).sheet().doRead();
            
            // 如果没有数据，直接返回空字符串
            if (allData.isEmpty()) {
                return "";
            }
            
            // 处理合并单元格：将合并的值填充到所有被合并的单元格位置
            processMergedCells(allData);
            
            // 将处理后的数据转换为CSV格式
            for (Map<Integer, Object> rowData : allData) {
                String csvLine = convertRowToCsv(rowData);
                csvContent.append(csvLine).append("\n");
            }
            
            return csvContent.toString();
        } catch (Exception e) {
            log.error("转换Excel到CSV失败", e);
            throw new RuntimeException("转换Excel到CSV失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 处理合并单元格：填充合并单元格的值到所有被合并的位置
     *
     * @param allData Excel的所有数据
     */
    private static void processMergedCells(List<Map<Integer, Object>> allData) {
        if (allData.isEmpty()) {
            return;
        }
        
        // 获取所有行和列的最大索引
        int maxRowIndex = allData.size() - 1;
        int maxColumnIndex = 0;
        
        for (Map<Integer, Object> rowData : allData) {
            for (Integer colIndex : rowData.keySet()) {
                if (colIndex > maxColumnIndex) {
                    maxColumnIndex = colIndex;
                }
            }
        }
        
        // 构建一个二维数组表示整个表格，便于检测和填充合并单元格
        Object[][] tableData = new Object[maxRowIndex + 1][maxColumnIndex + 1];
        
        // 填充已有的数据
        for (int rowIndex = 0; rowIndex <= maxRowIndex; rowIndex++) {
            Map<Integer, Object> rowData = allData.get(rowIndex);
            for (int colIndex = 0; colIndex <= maxColumnIndex; colIndex++) {
                tableData[rowIndex][colIndex] = rowData.get(colIndex);
            }
        }
        
        // 只处理垂直合并单元格
        for (int col = 0; col <= maxColumnIndex; col++) {
            // 跳过第3列(索引为2)的垂直合并单元格处理
            if (col == 2) {
                continue;
            }
            processVerticalMergedCells(tableData, allData, col, maxRowIndex);
        }
    }
    
    /**
     * 处理垂直合并单元格
     */
    private static void processVerticalMergedCells(Object[][] tableData, List<Map<Integer, Object>> allData, 
                                               int col, int maxRowIndex) {
        Object lastValue = null;
        int lastNonEmptyRow = -1;
        
        // 第一遍：标记非空值位置并记录值
        for (int row = 0; row <= maxRowIndex; row++) {
            Object value = tableData[row][col];
            if (value != null) {
                lastValue = value;
                lastNonEmptyRow = row;
            } else if (lastNonEmptyRow != -1) {
                // 当前是空单元格，检查是否是合并单元格的一部分
                // 寻找下一个非空单元格
                int nextNonEmptyRow = findNextNonEmptyRow(tableData, row, maxRowIndex, col);
                
                // 判断是否属于垂直合并
                if (nextNonEmptyRow == -1) {
                    // 后面没有非空单元格了，可能是一直到表格结束的合并单元格
                    // 用上一个非空值填充
                    fillVerticalRange(tableData, allData, lastNonEmptyRow + 1, maxRowIndex, col, lastValue);
                    break;
                } else {
                    // 检查当前空区域是否应该被填充为合并单元格
                    if (isVerticalMergeCandidate(tableData, lastNonEmptyRow, nextNonEmptyRow, col)) {
                        // 填充合并单元格
                        fillVerticalRange(tableData, allData, lastNonEmptyRow + 1, nextNonEmptyRow - 1, col, lastValue);
                        
                        // 跳到下一个非空行
                        row = nextNonEmptyRow - 1;  // -1是因为循环会+1
                    }
                    // 否则不填充，保持为空
                }
            }
        }
    }
    
    /**
     * 寻找下一个非空单元格的行索引
     */
    private static int findNextNonEmptyRow(Object[][] tableData, int startRow, int maxRowIndex, int col) {
        for (int row = startRow; row <= maxRowIndex; row++) {
            if (tableData[row][col] != null) {
                return row;
            }
        }
        return -1;  // 没有找到非空单元格
    }
    
    /**
     * 判断一个垂直区域是否是合并单元格的候选区域
     */
    private static boolean isVerticalMergeCandidate(Object[][] tableData, int startRow, int endRow, int col) {
        // 如果连续空白区域太大，可能不是合并单元格
         int emptyCount = endRow - startRow - 1;
        // if (emptyCount > 50) {  // 增大允许的空单元格数量，适应大范围合并单元格
        //     return false;
        // }
        
        // 如果一列中上下单元格内容连贯，通常表示这是垂直合并单元格
        // 检查上下文的模式来确定
        
        // 检查起始单元格和结束单元格是否具有相似的模式
        // 例如都是数字、都是文本，或者有内容关联
        Object startValue = tableData[startRow][col];
        Object endValue = tableData[endRow][col];
        
        // 检查右侧列的模式
        if (col + 1 < tableData[0].length) {
            // 检查右侧列的值是否形成模式
            boolean rightColConsistent = true;
            boolean allRightColEmpty = true;
            Object rightColFirstValue = null;
            
            for (int r = startRow; r <= endRow; r++) {
                Object rightVal = tableData[r][col + 1];
                
                if (rightVal != null) {
                    allRightColEmpty = false;
                    
                    if (rightColFirstValue == null) {
                        rightColFirstValue = rightVal;
                    } else if (!rightVal.equals(rightColFirstValue)) {
                        rightColConsistent = false;
                    }
                }
            }
            
            // 如果右侧列都是相同的值，或者都是空值，很可能是垂直合并单元格
            if (rightColConsistent || allRightColEmpty) {
                return true;
            }
        }
        
        // 检查连续性
        // 检查是否存在行结构模式（如表头-数据）
        // 第2列（索引为1）通常包含设备名称等可合并内容，应更宽松对待
        if (col == 1) {
            return true;  // 对第2列（B列）采用更宽松的检测标准
        }
        
        // 检查是否有其他列在同一行范围内有明显模式
        boolean hasPatternInOtherCols = false;
        for (int c = 0; c < tableData[0].length; c++) {
            if (c == col) continue;
            
            boolean colHasPattern = true;
            Object patternValue = null;
            
            for (int r = startRow + 1; r < endRow; r++) {
                Object val = tableData[r][c];
                if (val != null) {
                    if (patternValue == null) {
                        patternValue = val;
                    } else if (!val.equals(patternValue)) {
                        colHasPattern = false;
                        break;
                    }
                }
            }
            
            if (colHasPattern && patternValue != null) {
                hasPatternInOtherCols = true;
                break;
            }
        }
        
        if (hasPatternInOtherCols) {
            return true;
        }
        
        // 保守起见，对小范围空白区域更宽松一些
        return emptyCount <= 30;
    }
    
    /**
     * 填充垂直区域的合并单元格
     */
    private static void fillVerticalRange(Object[][] tableData, List<Map<Integer, Object>> allData, 
                                       int startRow, int endRow, int col, Object value) {
        for (int row = startRow; row <= endRow; row++) {
            tableData[row][col] = value;
            allData.get(row).put(col, value);
        }
    }
    
    /**
     * 将一行Excel数据转换为CSV格式的字符串
     *
     * @param rowData Excel的一行数据
     * @return CSV格式的行字符串
     */
    private static String convertRowToCsv(Map<Integer, Object> rowData) {
        StringJoiner joiner = new StringJoiner(",");
        
        // 获取最大列索引
        int maxColumnIndex = 0;
        for (Integer index : rowData.keySet()) {
            if (index > maxColumnIndex) {
                maxColumnIndex = index;
            }
        }
        
        // 遍历所有列
        for (int i = 0; i <= maxColumnIndex; i++) {
            Object cellValue = rowData.get(i);
            String cellStr = cellValue == null ? "" : cellValue.toString();
            
            // 如果单元格内容包含逗号、双引号或换行符，需要用双引号包围
            if (cellStr.contains(",") || cellStr.contains("\"") || cellStr.contains("\n")) {
                // 将单元格内的双引号替换为两个双引号（CSV转义规则）
                cellStr = cellStr.replace("\"", "\"\"");
                // 用双引号包围整个单元格内容
                cellStr = "\"" + cellStr + "\"";
            }
            
            joiner.add(cellStr);
        }
        
        return joiner.toString();
    }

} 