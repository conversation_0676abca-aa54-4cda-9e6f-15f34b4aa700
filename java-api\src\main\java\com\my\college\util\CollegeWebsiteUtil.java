package com.my.college.util;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.regex.Pattern;

/**
 * 大学网站工具类
 * 提供处理大学网站URL的实用方法
 * 
 * <AUTHOR>
 * @date 2024
 */
public class CollegeWebsiteUtil {

    private static final Pattern WWW_PATTERN = Pattern.compile("^www\\.", Pattern.CASE_INSENSITIVE);

    /**
     * 从完整的URL中提取主域名
     * 去除协议、www前缀、路径等，只保留主域名
     * 
     * 示例：
     * https://www.wisc.edu -> wisc.edu
     * https://www.stanford.edu/ -> stanford.edu
     * https://www.berkeley.edu/ -> berkeley.edu
     * 
     * @param fullUrl 完整的URL字符串
     * @return 提取出的主域名，如果URL格式不正确则返回null
     */
    public static String extractMainDomain(String fullUrl) {
        if (fullUrl == null || fullUrl.trim().isEmpty()) {
            return null;
        }

        try {
            // 如果URL不包含协议，添加默认的http协议
            String urlToProcess = fullUrl;
            if (!fullUrl.contains("://")) {
                urlToProcess = "http://" + fullUrl;
            }

            URL url = new URL(urlToProcess);
            String host = url.getHost();
            
            if (host == null) {
                return null;
            }

            // 去除www前缀（不区分大小写）
            String mainDomain = WWW_PATTERN.matcher(host).replaceFirst("");
            
            return mainDomain;
            
        } catch (MalformedURLException e) {
            // URL格式不正确，返回null
            return null;
        }
    }

    /**
     * 批量提取多个URL的主域名
     * 
     * @param urls URL数组
     * @return 对应的主域名数组
     */
    public static String[] extractMainDomains(String[] urls) {
        if (urls == null) {
            return null;
        }
        
        String[] domains = new String[urls.length];
        for (int i = 0; i < urls.length; i++) {
            domains[i] = extractMainDomain(urls[i]);
        }
        
        return domains;
    }

    /**
     * 检查是否为有效的大学域名
     * 简单检查是否以.edu结尾
     * 
     * @param domain 域名
     * @return 是否为教育域名
     */
    public static boolean isEducationDomain(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }
        
        return domain.toLowerCase().endsWith(".edu");
    }

    /**
     * 从URL提取主域名并检查是否为教育域名
     * 
     * @param fullUrl 完整URL
     * @return 是否为教育网站
     */
    public static boolean isEducationWebsite(String fullUrl) {
        String domain = extractMainDomain(fullUrl);
        return isEducationDomain(domain);
    }
} 