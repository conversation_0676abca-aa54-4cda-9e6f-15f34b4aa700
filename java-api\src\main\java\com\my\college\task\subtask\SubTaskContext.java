package com.my.college.task.subtask;

/**
 * 子任务上下文
 */
public class SubTaskContext {
	
	private static final ThreadLocal<String> TASK_ID_LOCAL = new ThreadLocal<String>();
    private static final ThreadLocal<String> SUBTASK_ID_LOCAL = new ThreadLocal<String>();
    private static final ThreadLocal<String> URL_LOCAL = new ThreadLocal<String>();

    
    /**
     * 设置任务id、子任务id
     * @param taskId
     * @param subTaskId
     */
    public static void set(String taskId, String subTaskId, String url) {
    	TASK_ID_LOCAL.set(taskId);
    	SUBTASK_ID_LOCAL.set(subTaskId);
    	URL_LOCAL.set(url);
    }    
    
    /**
     * 设置子任务id
     * @param subTaskId
     */
    public static void set(String subTaskId) {
    	SUBTASK_ID_LOCAL.set(subTaskId);
    }    
    
    /**
     * 清除环境
     * 防止内存泄漏，在异步任务结束时调用
     */
    public static void remove() {
    	TASK_ID_LOCAL.remove();
    	SUBTASK_ID_LOCAL.remove();
    	URL_LOCAL.remove();
    }
    
    /**
     * 获取任务id
     * @return
     */
    public static String getTaskId() {
    	return TASK_ID_LOCAL.get();
    }

    /**
     * 获取子任务id
     * @return
     */
    public static String getSubTaskId() {
    	return SUBTASK_ID_LOCAL.get();
    }
    
    /**
     * 获取url
     * @return
     */
    public static String getUrl() {
    	return URL_LOCAL.get();
    }

}
