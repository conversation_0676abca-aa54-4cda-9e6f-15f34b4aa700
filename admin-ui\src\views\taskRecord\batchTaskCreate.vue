<template>
  <div>
    <!-- breadcrumb -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">系统管理</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/taskRecord' }">任务记录</el-breadcrumb-item>
      <el-breadcrumb-item>创建任务</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主要内容区域 -->
    <div class="batch-task-container">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>选择学校</span>
        </div>

        <!-- 穿梭框 -->
        <div class="transfer-container" id="myTransfer">
          <el-transfer v-model="selectedSchools" :data="schoolData" :titles="['全部学校', '已选学校']"
            :button-texts="['', '']" :render-content="renderFunc" :format="{
              noChecked: '${total}',
              hasChecked: '${checked}/${total}'
            }" @change="handleChange" filterable :filter-placeholder="'请输入学校名称'"
            class="wide-transfer"
            style="text-align: left; display: inline-block">
          </el-transfer>
        </div>

        <!-- 操作按钮 -->
        <div class="button-container">
          <el-button type="primary" @click="submitBatchTask" :loading="submitting">
            <i class="el-icon-check"></i>
            创建任务
          </el-button>
          <el-button @click="goBack">
            <i class="el-icon-back"></i>
            返回
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { schoolList } from '../../api/school'
import { createBatchTask } from '../../api/taskRecord'
import { loadToken } from '../../utils/util'

export default {
  name: 'BatchTaskCreate',
  data() {
    return {
      loading: false,
      submitting: false,
      schoolData: [], // 所有学校数据
      selectedSchools: [], // 已选择的学校ID列表
      userEntity: undefined
    }
  },

  created() {
    // 加载用户缓存
    this.userEntity = JSON.parse(localStorage.getItem('userEntity'));
    // 加载学校数据
    this.loadSchoolData();
  },

  mounted() {
    // 动态设置穿梭框宽度
    this.$nextTick(() => {
      this.setTransferPanelWidth();
    });
  },

  methods: {
    // 加载学校数据
    loadSchoolData() {
      this.loading = true;
      const params = {
        token: loadToken()
      };

      schoolList(params)
        .then(res => {
          this.loading = false;
          if (res.data && Array.isArray(res.data)) {
            // 转换数据格式为穿梭框需要的格式
            this.schoolData = res.data.map(school => ({
              key: school.schoolId,
              label: `${school.schoolId} - ${school.chineseName}`,
              schoolId: school.schoolId,
              chineseName: school.chineseName
            }));
            // 数据加载完成后设置宽度
            this.$nextTick(() => {
              this.setTransferPanelWidth();
            });
          } else {
            this.$message.error('获取学校数据失败');
          }
        })
        .catch(error => {
          this.loading = false;
          console.error('加载学校数据失败:', error);
          this.$message.error('加载学校数据失败');
        });
    },

    // 自定义渲染函数
    renderFunc(h, option) {
      return h('span', null, `${option.schoolId} - ${option.chineseName}`);
    },

    // 穿梭框变化处理
    handleChange(value, direction, movedKeys) {
      console.log('选择变化:', value, direction, movedKeys);
      // 确保宽度设置
      this.$nextTick(() => {
        this.setTransferPanelWidth();
      });
    },

    // 提交批量任务
    submitBatchTask() {
      if (this.selectedSchools.length === 0) {
        this.$message.warning('请至少选择一个学校');
        return;
      }

      this.$confirm(`确定要为选中的 ${this.selectedSchools.length} 个学校创建批量任务吗？`, '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.doSubmitBatchTask();
      }).catch(() => {
        this.$message.info('已取消操作');
      });
    },

    // 执行提交批量任务
    doSubmitBatchTask() {
      this.submitting = true;

      const params = {
        schoolIdList: this.selectedSchools,
        token: loadToken()
      };

      createBatchTask(params)
        .then(res => {
          this.submitting = false;
          this.$message.success('批量任务创建成功');
          // 返回任务记录页面
          this.$router.push('/taskRecord');
        })
        .catch(error => {
          this.submitting = false;
          console.error('创建批量任务失败:', error);
        });
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 动态设置穿梭框宽度和高度
    setTransferPanelWidth() {
      const panels = document.querySelectorAll('.el-transfer-panel');
      panels.forEach(panel => {
        panel.style.width = '401px';
        panel.style.minWidth = '401px';
        panel.style.maxWidth = 'none';
        panel.style.height = '500px';
        panel.style.minHeight = '500px';
      });

      // 设置穿梭框面板内容区域的高度
      const bodies = document.querySelectorAll('.el-transfer-panel__body');
      bodies.forEach(body => {
        body.style.height = 'calc(500px - 40px)';
      });

      // 设置穿梭框列表的高度
      const lists = document.querySelectorAll('.el-transfer-panel__list');
      lists.forEach(list => {
        list.style.height = '100%';
        list.style.overflowY = 'auto';
      });
    }
  }
}
</script>

<style scoped>
.batch-task-container {
  margin-top: 20px;
}

.box-card {
  max-width: 1000px;
  margin: 0 auto;
}

.transfer-container {
  text-align: center;
  margin: 20px 0;
}

/* 使用深度选择器设置穿梭框面板高度 */
/deep/ .el-transfer-panel {
  height: 500px !important;
  width: 401px !important;
  min-width: 401px !important;
  max-width: none !important;
  min-height: 500px !important;
}

/* 或者使用 ::v-deep (Vue 2.7+ 和 Vue 3) */
::v-deep .el-transfer-panel {
  height: 500px !important;
  width: 401px !important;
  min-width: 401px !important;
  max-width: none !important;
  min-height: 500px !important;
}

/* 设置穿梭框面板内容区域的高度 */
/deep/ .el-transfer-panel__body {
  height: calc(500px - 40px) !important; /* 减去头部高度 */
}

::v-deep .el-transfer-panel__body {
  height: calc(500px - 40px) !important;
}

/* 设置穿梭框列表的高度 */
/deep/ .el-transfer-panel__list {
  height: 100% !important;
  overflow-y: auto !important;
}

::v-deep .el-transfer-panel__list {
  height: 100% !important;
  overflow-y: auto !important;
}

.button-container {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  margin-bottom: 20px;
}

.button-container .el-button {
  margin: 0 10px;
  min-width: 120px;
}

/* 穿梭框样式调整 */
.el-transfer {
  text-align: center;
}

/* Vue 2 中的深度选择器 */
/deep/ .el-transfer-panel {
  width: 401px !important;
  min-width: 401px !important;
  max-width: none !important;
  height: 500px !important;
  min-height: 500px !important;
}

/* 或者使用 ::v-deep (Vue 2.7+ 和 Vue 3) */
::v-deep .el-transfer-panel {
  width: 401px !important;
  min-width: 401px !important;
  max-width: none !important;
  height: 500px !important;
  min-height: 500px !important;
}

/* 更强的选择器覆盖 */
.el-transfer .el-transfer-panel {
  width: 401px !important;
  min-width: 401px !important;
  max-width: none !important;
}

/* 针对具体的transfer容器 */
.transfer-container .el-transfer .el-transfer-panel {
  width: 401px !important;
  min-width: 401px !important;
  max-width: none !important;
}

.el-transfer-panel__header {
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.el-transfer-panel__item {
  padding: 8px 15px;
}

.el-transfer-panel__item:hover {
  background-color: #f5f7fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-transfer-panel {
    width: 350px !important;
    min-width: 350px !important;
    height: 450px !important;
    min-height: 450px !important;
  }

  .box-card {
    margin: 0 10px;
  }
}

/* 强制覆盖Element UI默认样式 */
.el-transfer-panel {
  width: 401px !important;
  min-width: 401px !important;
  max-width: none !important;
  height: 500px !important;
  min-height: 500px !important;
}

/* 针对自定义类的样式 */
.wide-transfer .el-transfer-panel {
  width: 401px !important;
  min-width: 401px !important;
  max-width: none !important;
  height: 500px !important;
  min-height: 500px !important;
}

/* 更具体的选择器 */
.transfer-container .wide-transfer .el-transfer-panel {
  width: 401px !important;
  min-width: 401px !important;
  max-width: none !important;
  height: 500px !important;
  min-height: 500px !important;
}

/* 针对batchTaskCreate页面的特定样式 */
#myTransfer .el-transfer-panel {
  width: 401px !important;
  min-width: 401px !important;
  max-width: none !important;
  height: 500px !important;
  min-height: 500px !important;
}

#myTransfer .el-transfer-panel__body {
  height: calc(500px - 40px) !important;
}

#myTransfer .el-transfer-panel__list {
  height: 100% !important;
  overflow-y: auto !important;
}
</style>
