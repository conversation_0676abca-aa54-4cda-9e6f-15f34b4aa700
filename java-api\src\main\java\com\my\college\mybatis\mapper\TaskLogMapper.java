package com.my.college.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.college.controller.dto.task_log.TaskLogPageDTO;
import com.my.college.controller.vo.task_log.TaskLogPageVO;
import com.my.college.mybatis.entity.TaskLog;

/**
 * 任务日志表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-05-03
 */
public interface TaskLogMapper extends BaseMapper<TaskLog> {

	/**
	 * 分页
	 * @param pageDTO 查询参数
	 */
	Page<TaskLogPageVO> page(TaskLogPageDTO pageDTO);
	
}
