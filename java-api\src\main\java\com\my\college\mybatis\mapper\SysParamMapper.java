package com.my.college.mybatis.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.my.college.enums.SysParamFieldEnum;
import com.my.college.mybatis.entity.SysParam;

/**
 * 系统参数配置表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-05-01
 */
public interface SysParamMapper extends BaseMapper<SysParam> {

	/**
	 * 查询批次号
	 * @return
	 */
	default Integer batchNumber() {
		Wrapper<SysParam> queryWrapper = Wrappers.lambdaQuery(SysParam.class)
				.eq(SysParam::getK, "batchNumber");
		SysParam entity = this.selectOne(queryWrapper);
		Integer batchNumber = Integer.parseInt(entity.getV());
		return batchNumber;
	}

	
}
