package com.my.college.controller.dto.title;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import com.alibaba.fastjson2.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.college.controller.vo.title.TitlePageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_标题表
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class TitlePageDTO 
						extends PageDTO<TitlePageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
	@NotBlank(message="title不能为空")
    private String title;

}
