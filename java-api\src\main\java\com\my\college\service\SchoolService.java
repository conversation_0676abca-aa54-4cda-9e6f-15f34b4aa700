package com.my.college.service;

import com.my.college.mybatis.entity.School;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Set;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.college.controller.dto.school.SchoolInsertDTO;
import com.my.college.controller.dto.school.SchoolPageDTO;
import com.my.college.controller.dto.school.SchoolUpdateDTO;
import com.my.college.controller.dto.school.SchoolUploadDTO;
import com.my.college.controller.dto.school.SchoolDeleteDTO;
import com.my.college.controller.vo.school.SchoolDetailVO;
import com.my.college.controller.vo.school.SchoolPageVO;

/**
 * 学校表 服务类
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface SchoolService extends IService<School> {

	/**
	 * 新增
	 */
	void insert(SchoolInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(SchoolUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	SchoolDetailVO detail(Integer id);

	/**
	 * 分页
	 */
	Page<SchoolPageVO> page(SchoolPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(SchoolDeleteDTO deleteDTO);

	/**
	 * 批量新增
	 * @param validList
	 */
	void batchInsert(Set<SchoolUploadDTO> validList);	

	/**
	 * 根据中文名称查询学校
	 * @param chineseName 中文名称
	 * @return 学校实体
	 */
	School getByChineseName(String chineseName);

}
