<template>
  <div>
    <!-- breadcrumb -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">系统管理</el-breadcrumb-item>
      <el-breadcrumb-item>图标展示</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- search -->
    <el-form :inline="true" :model="formInline" class="icon-search" ref="searchForm">
      <el-form-item label="搜索图标：">
        <el-input size="small" v-model="formInline.keyword" placeholder="图标名称" clearable style="width: 190px;" @input="filterIcons"></el-input>
      </el-form-item>
    </el-form>

    <!-- icon display -->
    <div class="icon-container">
      <div class="icon-item" v-for="(icon, index) in filteredIcons" :key="index + '-' + icon.name" @click="copyIconName(icon.name)">
        <div class="icon-display">
          <i class="iconfont" :class="icon.name"></i>
        </div>
        <div class="icon-name">{{ icon.name }}</div>
        <div class="icon-code">{{ icon.code }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'IconDisplay',
  data() {
    return {
      formInline: {
        keyword: ''
      },
      icons: [
        { name: 'li-icon-cal', code: '\\e78c' },
        { name: 'icon-cat-skuQuery', code: '\\e631' },
        { name: 'li-icon-gongsiguanli', code: '\\e704' },
        { name: 'li-icon-shujujiankong', code: '\\e65d' },
        { name: 'icon-order-manage', code: '\\e63a' },
        { name: 'li-icon-zhifuguanli', code: '\\e6f8' },
        { name: 'li-icon-xiangmuguanli', code: '\\e651' },
        { name: 'icon-cat-skuBasic', code: '\\e622' },
        { name: 'li-icon-dingdanguanli', code: '\\e728' },
        { name: 'icon-provider-manage', code: '\\e62b' },
        { name: 'icon-cat-skuInfo', code: '\\e620' },
        { name: 'icon-cms-refri', code: '\\e609' },
        { name: 'icon-promotion-manage', code: '\\e67b' },
        { name: 'icon-news-manage', code: '\\e684' },
        { name: 'icon-cus-manage', code: '\\e621' },
        { name: 'icon-cs-manage', code: '\\e626' },
        { name: 'li-icon-zhifupeizhi1', code: '\\e623' },
        { name: 'icon-cms-manage', code: '\\e66e' },
        { name: 'li-icon-xitongguanli', code: '\\e629' },
        { name: 'icon-cms-manage2', code: '\\e652' },
        { name: 'li-icon-jichuguanli', code: '\\e625' },
      ],
      filteredIcons: []
    }
  },
  created() {
    this.filteredIcons = this.icons;
  },
  methods: {
    filterIcons() {
      const keyword = this.formInline.keyword.toLowerCase();
      if (!keyword) {
        this.filteredIcons = this.icons;
      } else {
        this.filteredIcons = this.icons.filter(icon =>
          icon.name.toLowerCase().includes(keyword)
        );
      }
    },
    copyIconName(iconName) {
      const el = document.createElement('textarea');
      el.value = iconName;
      document.body.appendChild(el);
      el.select();
      document.execCommand('copy');
      document.body.removeChild(el);
      this.$message.success(`已复制: ${iconName}`);
    }
  }
}
</script>

<style scoped>
.icon-search {
  margin-top: 20px;
  margin-bottom: 20px;
}

.icon-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120px;
  height: 120px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.icon-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.icon-display {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
}

.iconfont {
  font-size: 30px;
}

.icon-name {
  margin-top: 10px;
  font-size: 12px;
  text-align: center;
  color: #606266;
  word-break: break-all;
}

.icon-code {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
}
</style>
