package com.my.college.forest.deepseek;

import java.util.List;
import java.util.Map;

import com.dtflys.forest.annotation.Address;
import com.dtflys.forest.annotation.BodyType;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Headers;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Var;
import com.my.college.forest.deepseek.dto.format.DeepSeekResponseFormat;
import com.my.college.forest.deepseek.dto.message.DeepSeekMessage;

/**
 * DeepSeek API客户端
 * 
 * <AUTHOR>
 * @date 2025-05-01
 */
@Address(host = "api.deepseek.com", scheme = "https")
@Deprecated
public interface DeepSeekClient {

    /**
     * 会话补全
     * 
     * @param apiKey DeepSeek API密钥
     * @return API响应内容
     */
    @Post(url = "/chat/completions", readTimeout = 15 * 60 * 1000, connectTimeout =  15 * 60 * 1000)
    @Headers({
        "Content-Type: application/json",
        "Authorization: Bearer ${apiKey}"
    })
    @BodyType("json")
    Map<String, Object> chatCompletions(
        @JSONBody("model") String model,
        @JSONBody("messages") List<DeepSeekMessage> messages,
        @JSONBody("max_tokens") Integer maxTokens,
        @JSONBody("temperature") Double temperature,
        @JSONBody("response_format") DeepSeekResponseFormat responseFormat,
        @Var("apiKey") String apiKey
    );
    
    /**
     * 查询用户余额
     * 
     * @param apiKey DeepSeek API密钥
     * @return 余额信息
     */
    @Get(url = "/user/balance")
    @Headers({
        "Authorization: Bearer ${apiKey}"
    })
    Map<String, Object> getUserBalance(@Var("apiKey") String apiKey);
} 