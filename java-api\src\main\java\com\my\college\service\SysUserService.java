package com.my.college.service;

import javax.validation.Valid;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.my.college.controller.dto.sys_user.SysUserChangeMyPasswordDTO;
import com.my.college.controller.dto.sys_user.SysUserChangePasswordDTO;
import com.my.college.controller.dto.sys_user.SysUserDeleteDTO;
import com.my.college.controller.dto.sys_user.SysUserInsertDTO;
import com.my.college.controller.dto.sys_user.SysUserPageDTO;
import com.my.college.controller.dto.sys_user.SysUserUpdateDTO;
import com.my.college.controller.vo.sys_user.SysUserPageVO;
import com.my.college.mybatis.entity.SysUser;

/**
 * system user 服务类
 *
 * @date 2024-05-20
 */
public interface SysUserService extends IService<SysUser> {

	/**
	 * 查询用户
	 * @param username
	 * @param password
	 * @return
	 */
	SysUser getByUsernameAndPassword(String username, String password);
	
	/**
	 * 查询用户
	 * @param id
	 * @return
	 */
	SysUser get(String userId);
	
	/**
	 * 修改我的密码
	 * @param dto
	 */
	void changeMyPassword(@Valid SysUserChangeMyPasswordDTO dto);

	/**
	 * 修改指定用户密码
	 * @param dto
	 */
	void changePassword(@Valid SysUserChangePasswordDTO dto);

	/**
	 * 新增用户
	 * @param dto
	 */
	void insert(@Valid SysUserInsertDTO dto);

	/**
	 * 修改用户
	 * @param dto
	 */
	void update(@Valid SysUserUpdateDTO dto);

	/**
	 * 分页查询
	 * @param dto
	 * @return
	 */
	Page<SysUserPageVO> page(SysUserPageDTO dto);

	/**
	 * 删除用户
	 * @param dto
	 */
	void delete(@Valid SysUserDeleteDTO dto);
}
