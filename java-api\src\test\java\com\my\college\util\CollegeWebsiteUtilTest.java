package com.my.college.util;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

/**
 * CollegeWebsiteUtil 测试类
 */
public class CollegeWebsiteUtilTest {

    @Test
    public void testExtractMainDomain() {
        // 测试基本功能
        assertEquals("wisc.edu", CollegeWebsiteUtil.extractMainDomain("https://www.wisc.edu"));
        assertEquals("stanford.edu", CollegeWebsiteUtil.extractMainDomain("https://www.stanford.edu/"));
        assertEquals("berkeley.edu", CollegeWebsiteUtil.extractMainDomain("https://www.berkeley.edu/"));
        assertEquals("caltech.edu", CollegeWebsiteUtil.extractMainDomain("https://www.caltech.edu/"));
        assertEquals("yale.edu", CollegeWebsiteUtil.extractMainDomain("https://www.yale.edu/"));
        assertEquals("cornell.edu", CollegeWebsiteUtil.extractMainDomain("https://www.cornell.edu/"));
        
        // 测试不同协议
        assertEquals("mit.edu", CollegeWebsiteUtil.extractMainDomain("http://www.mit.edu"));
        assertEquals("harvard.edu", CollegeWebsiteUtil.extractMainDomain("https://www.harvard.edu"));
        
        // 测试无www前缀
        assertEquals("princeton.edu", CollegeWebsiteUtil.extractMainDomain("https://princeton.edu"));
        
        // 测试无协议
        assertEquals("columbia.edu", CollegeWebsiteUtil.extractMainDomain("www.columbia.edu"));
        assertEquals("upenn.edu", CollegeWebsiteUtil.extractMainDomain("upenn.edu"));
        
        // 测试边界情况
        assertNull(CollegeWebsiteUtil.extractMainDomain(null));
        assertNull(CollegeWebsiteUtil.extractMainDomain(""));
        assertNull(CollegeWebsiteUtil.extractMainDomain("   "));
        
        // 返回原始内容
//        String extractMainDomain = CollegeWebsiteUtil.extractMainDomain("invalid-url");
//        System.out.println(extractMainDomain);
//		assertNull(extractMainDomain);
    }

    @Test
    public void testExtractMainDomains() {
        String[] urls = {
            "https://www.wisc.edu",
            "https://www.stanford.edu/",
            "https://www.berkeley.edu/"
        };
        
        String[] expected = {"wisc.edu", "stanford.edu", "berkeley.edu"};
        String[] result = CollegeWebsiteUtil.extractMainDomains(urls);
        
        assertArrayEquals(expected, result);
        
        // 测试null输入
        assertNull(CollegeWebsiteUtil.extractMainDomains(null));
    }

    @Test
    public void testIsEducationDomain() {
        assertTrue(CollegeWebsiteUtil.isEducationDomain("wisc.edu"));
        assertTrue(CollegeWebsiteUtil.isEducationDomain("stanford.edu"));
        assertTrue(CollegeWebsiteUtil.isEducationDomain("BERKELEY.EDU")); // 测试大小写不敏感
        
        assertFalse(CollegeWebsiteUtil.isEducationDomain("google.com"));
        assertFalse(CollegeWebsiteUtil.isEducationDomain("facebook.com"));
        assertFalse(CollegeWebsiteUtil.isEducationDomain(null));
        assertFalse(CollegeWebsiteUtil.isEducationDomain(""));
    }

    @Test
    public void testIsEducationWebsite() {
        assertTrue(CollegeWebsiteUtil.isEducationWebsite("https://www.wisc.edu"));
        assertTrue(CollegeWebsiteUtil.isEducationWebsite("https://www.stanford.edu/"));
        
        assertFalse(CollegeWebsiteUtil.isEducationWebsite("https://www.google.com"));
        assertFalse(CollegeWebsiteUtil.isEducationWebsite("https://www.facebook.com"));
    }
} 