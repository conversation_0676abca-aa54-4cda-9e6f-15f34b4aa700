package com.my.college.controller.vo.address_book;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_通讯录表
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
//easyExcel注解参考：https://www.cnblogs.com/bluekang/p/13438666.html#!comments
public class AddressBookPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "编号")
	@ColumnWidth(8)   
    private Integer id;

    /**
     * 学校ID
     */
    @ExcelIgnore
    private Integer schoolId;
    
    /**
     * 学校
     */
    @ExcelProperty(value = "学校")
	@ColumnWidth(22)   
    private String schoolName;

    /**
     * 部门
     */
    @ExcelProperty(value = "部门")
	@ColumnWidth(20)   
    private String department;

    /**
     * 联系人姓名
     */
    @ExcelProperty(value = "联系人姓名")
	@ColumnWidth(22)   
    private String contactName;

    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱")
	@ColumnWidth(24)      
    private String email;

    /**
     * 职位
     */
    @ExcelProperty(value = "职位")
	@ColumnWidth(24)     
    private String position;

    /**
     * 电话号码
     */
    @ExcelProperty(value = "电话号码")
	@ColumnWidth(18)     
    private String phone;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间", format = "yyyy-MM-dd")
	@ColumnWidth(22)    
    private LocalDateTime updateTime;

    /**
     * 是否手动添加(0-否,1-是)
     */
    @ExcelProperty(value = "手动")
	@ColumnWidth(6)        
    private Boolean manualFlag;

    /**
     * 数据来源url
     */
    @ExcelProperty(value = "数据来源url")
    @ColumnWidth(50)     
    private String url;

}
