package com.my.college.controller.dto.position;

import java.io.Serializable;

import com.alibaba.fastjson2.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.college.controller.vo.position.PositionPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_职位表
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class PositionPageDTO 
						extends PageDTO<PositionPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 职位
     */
    private String position;

}
