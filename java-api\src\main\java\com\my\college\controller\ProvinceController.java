package com.my.college.controller;


import java.util.List;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.college.controller.dto.province.ProvinceDeleteDTO;
import com.my.college.controller.dto.province.ProvinceInsertDTO;
import com.my.college.controller.dto.province.ProvincePageDTO;
import com.my.college.controller.dto.province.ProvinceUpdateDTO;
import com.my.college.controller.vo.StdResp;
import com.my.college.controller.vo.province.ProvinceDetailVO;
import com.my.college.controller.vo.province.ProvincePageVO;
import com.my.college.mybatis.entity.Province;
import com.my.college.service.ProvinceService;

import lombok.RequiredArgsConstructor;

/**
 * 美国50个州州名 
 * 
 * <AUTHOR>
 * @date 2025-05-25
 */
@RestController
@RequestMapping("/api/province")
@RequiredArgsConstructor
public class ProvinceController {

    private final ProvinceService provinceService;

    /**
    * 新增
    */
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody ProvinceInsertDTO insertDTO) {
    	this.provinceService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @PutMapping("")
    public StdResp<?> update(@Valid @RequestBody ProvinceUpdateDTO updateDTO) {
    	this.provinceService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<ProvinceDetailVO> detail(@PathVariable String id) {
    	return StdResp.success(this.provinceService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<ProvincePageVO>> page(ProvincePageDTO pageDTO) {
        Page<ProvincePageVO> page = this.provinceService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
     * 查询全部
     */
    @GetMapping(value = "list")
    public StdResp<List<Province>> list() {
    	List<Province> data = this.provinceService.list();
    	return StdResp.success(data);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody ProvinceDeleteDTO deleteDTO) {
    	this.provinceService.delete(deleteDTO);
		return StdResp.success();
    }
    
}
