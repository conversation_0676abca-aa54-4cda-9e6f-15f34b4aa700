package com.my.college.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.my.college.controller.dto.sys_param.SysParamUpdateDTO;
import com.my.college.controller.vo.sys_param.SysParamVO;
import com.my.college.mybatis.entity.SysParam;

/**
 * 系统参数配置表 服务类
 *
 * <AUTHOR>
 * @date 2025-05-01
 */
public interface SysParamService extends IService<SysParam> {

	/**
	 * 修改
	 */
	void update(SysParamUpdateDTO updateDTO);

	/**
	 * 查询详情
	 */
	SysParamVO get();

	/**
	 * 批次号+1
	 * @return
	 */
	Integer nextBatchNumber();

}
