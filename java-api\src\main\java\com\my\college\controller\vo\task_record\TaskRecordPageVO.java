package com.my.college.controller.vo.task_record;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_任务记录表
 *
 * <AUTHOR>
 * @date 2025-05-03
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class TaskRecordPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务编号
     */
    private String taskId;
    
    /**
     * 批次号
     */
    private Integer batchNumber;

    /**
     * 学校ID
     */
    private Integer schoolId;

    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    private LocalDateTime endTime;

    /**
     * 任务完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 任务状态(RUN, FAIL, SUCCESS)
     */
    private String status;
    
    /**
     * 任务失败备注
     */
    private String failRemark;

    /**
     * 请求
     */
    private String request;
    
    /**
     * 响应
     */
    private String response;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
