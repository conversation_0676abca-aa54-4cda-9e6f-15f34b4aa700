package com.my.college.controller.vo.sub_task_record;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 查看实体详情_子任务记录表
 *
 * <AUTHOR>
 * @date 2025-06-22
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SubTaskRecordDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 子任务编号
     */
    private String subTaskId;

    /**
     * 主任务编号
     */
    private String taskId;

    /**
     * 子任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 子任务结束时间
     */
    private LocalDateTime endTime;

    /**
     * 子任务完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 网址
     */
    private String url;

    /**
     * markdown请求
     */
    private String markdownRequest;

    /**
     * markdown响应
     */
    private String markdownResponse;

    /**
     * 请求参数(系统提示词)
     */
    private String requestSystemPrompt;

    /**
     * 请求参数(用户提示词)
     */
    private String requestUserPrompt;

    /**
     * 响应内容
     */
    private String response;

    /**
     * 响应内容(推理详情)
     */
    private String responseReasoning;

    /**
     * 响应内容(token消耗情况)
     */
    private String responseTokenCost;

    /**
     * 子任务状态(RUN, FAIL, SUCCESS, REBORN)
     */
    private String status;

    /**
     * 子任务失败备注
     */
    private String failRemark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
