package com.my.college.service;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.alibaba.fastjson2.JSON;
import com.my.college.controller.vo.sys_param.SysParamCheckVO;

import lombok.extern.slf4j.Slf4j;


@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
class SysParamServiceTest {
	
	@Autowired
	SysParamService sysParamService;

	
	@Test
	void testCheck() {
		SysParamCheckVO check = this.sysParamService.get().check();
		log.info(JSON.toJSONString(check));
	}

}
