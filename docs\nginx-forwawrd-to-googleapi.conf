server {
    listen 80;
    server_name _;

    # forward to googleapi
    location /customsearch/v1 {
        resolver ******* valid=30s;

        proxy_pass https://customsearch.googleapis.com/customsearch/v1/;
        proxy_ssl_server_name on;
        proxy_ssl_name customsearch.googleapis.com;

        proxy_set_header Host               customsearch.googleapis.com;
        proxy_set_header X-Real-IP          $remote_addr;
        proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto  https;
    }
}
