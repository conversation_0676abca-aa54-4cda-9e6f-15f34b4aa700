package com.my.college.controller.dto.sub_task_record;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 新增_子任务记录表
 *
 * <AUTHOR>
 * @date 2025-06-22
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SubTaskRecordInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主任务编号
     */
	@NotNull(message="taskId不能为空")
    private String taskId;

    /**
     * 子任务开始时间
     */
	@NotNull(message="startTime不能为空")
    private LocalDateTime startTime;

    /**
     * 子任务结束时间
     */
	@NotNull(message="endTime不能为空")
    private LocalDateTime endTime;

    /**
     * 子任务完成时间
     */
	@NotNull(message="finishTime不能为空")
    private LocalDateTime finishTime;

    /**
     * 网址
     */
	@NotNull(message="url不能为空")
    private String url;

    /**
     * markdown内容
     */
	@NotNull(message="markdown不能为空")
    private String markdown;

    /**
     * 请求参数
     */
	@NotNull(message="request不能为空")
    private String request;

    /**
     * 请求参数(系统提示词)
     */
	@NotNull(message="requestSystemPrompt不能为空")
    private String requestSystemPrompt;

    /**
     * 请求参数(用户提示词)
     */
	@NotNull(message="requestUserPrompt不能为空")
    private String requestUserPrompt;

    /**
     * 响应内容
     */
	@NotNull(message="response不能为空")
    private String response;

    /**
     * 响应内容(推理详情)
     */
	@NotNull(message="responseReasoning不能为空")
    private String responseReasoning;

    /**
     * 响应内容(token消耗情况)
     */
	@NotNull(message="responseTokenCost不能为空")
    private String responseTokenCost;

    /**
     * 子任务状态(RUN, FAIL, SUCCESS, REBORN)
     */
	@NotNull(message="status不能为空")
    private String status;

    /**
     * 子任务失败备注
     */
	@NotNull(message="failRemark不能为空")
    private String failRemark;

    /**
     * 创建时间
     */
	@NotNull(message="createTime不能为空")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
	@NotNull(message="updateTime不能为空")
    private LocalDateTime updateTime;

}
