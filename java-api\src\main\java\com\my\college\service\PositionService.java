package com.my.college.service;

import com.my.college.mybatis.entity.Position;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.college.controller.dto.position.PositionInsertDTO;
import com.my.college.controller.dto.position.PositionPageDTO;
import com.my.college.controller.dto.position.PositionDeleteDTO;
import com.my.college.controller.vo.position.PositionDetailVO;
import com.my.college.controller.vo.position.PositionPageVO;

import java.util.List;

/**
 * 职位表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface PositionService extends IService<Position> {

	/**
	 * 新增
	 */
	void insert(PositionInsertDTO insertDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	PositionDetailVO detail(String id);

	/**
	 * 分页
	 */
	Page<PositionPageVO> page(PositionPageDTO pageDTO);

	/**
	 * 查询全部职位
	 */
	List<Position> list();

	/**
	 * 批量删除(物理删除)
	 */
	void delete(PositionDeleteDTO deleteDTO);

}
