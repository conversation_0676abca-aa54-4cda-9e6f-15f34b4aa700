package com.my.college.service.check.impl;

import java.lang.reflect.Field;

import com.my.college.service.check.AbstractCheck;
import com.my.college.service.check.result.CheckResult;

import cn.hutool.core.util.StrUtil;

/**
 * markdown超时检测器
 * <AUTHOR>
 */
public class MarkdownTimeoutCheck extends AbstractCheck {

    @Override
    protected CheckResult doCheck(Object fieldValue, Field field, Object obj) {
    	String str = (String) fieldValue;
    	if (StrUtil.isBlank(str)) {
    		return fail(this.fieldLabel + "不能为空");
    	}
    	
        Integer markdownTimeout = new Integer(str);
        if (markdownTimeout <= 0) {
            return fail(this.fieldLabel + "必须大于零");
        }
        
        return success();
    }
    
}
