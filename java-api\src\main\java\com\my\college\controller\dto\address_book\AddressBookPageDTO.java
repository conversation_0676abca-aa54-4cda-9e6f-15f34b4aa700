package com.my.college.controller.dto.address_book;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.alibaba.fastjson2.annotation.JSONType;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.my.college.controller.vo.address_book.AddressBookPageVO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页_通讯录表
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@JSONType(ignores ={"total", "hitCount", "searchCount", "pages", "optimizeCountSql", "records", "countId", "maxLimit"})
@Data
@EqualsAndHashCode(callSuper = false)
public class AddressBookPageDTO 
						extends PageDTO<AddressBookPageVO> 
						implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 学校ID
     */
    private Integer schoolId;

    /**
     * 部门
     */
    private String department;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 职位
     */
    private String position;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否手动添加(0-否,1-是)
     */
    private Boolean manualFlag;

}
