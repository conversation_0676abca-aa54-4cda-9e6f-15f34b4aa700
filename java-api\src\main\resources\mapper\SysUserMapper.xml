<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.my.college.mybatis.mapper.SysUserMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.my.college.mybatis.entity.SysUser">
        <id column="username" property="username" />
        <result column="password" property="password" />
        <result column="admin_flag" property="adminFlag" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        username, password, is_admin, real_name, avatar
    </sql>

	<!-- 分页 -->    
    <select id="page" resultType="com.my.college.controller.vo.sys_user.SysUserPageVO">
		SELECT
			username, password, admin_flag, remark
		FROM
			sys_user AS t1
		<where>
        	1=1
	        <if test="username != null and username != ''">
	           	AND t1.username = #{username}
            </if>
	        <if test="password != null and password != ''">
	           	AND t1.password = #{password}
            </if>
        </where>
    </select>

</mapper>
