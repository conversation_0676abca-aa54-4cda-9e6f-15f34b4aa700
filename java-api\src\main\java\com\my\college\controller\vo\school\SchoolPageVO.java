package com.my.college.controller.vo.school;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 查看实体详情_学校表
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SchoolPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "编号")
	@ColumnWidth(8)
    private Integer schoolId;
    
    /**
     * 省份缩写
     */
    @ExcelProperty(value = "省份缩写")
	@ColumnWidth(12)    
    private String provinceId;

    /**
     * 中文名称
     */
    @ExcelProperty(value = "中文名称")
	@ColumnWidth(22)
    private String chineseName;

    /**
     * 英文名称
     */
    @ExcelProperty(value = "英文名称")
	@ColumnWidth(24)    
    private String englishName;

    /**
     * 网址
     */
    @ExcelProperty(value = "网址")
	@ColumnWidth(28)
    private String website;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
	@ColumnWidth(26)   
    private String remark;
    
    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
	@ColumnWidth(22)
    private LocalDateTime updateTime;

}
