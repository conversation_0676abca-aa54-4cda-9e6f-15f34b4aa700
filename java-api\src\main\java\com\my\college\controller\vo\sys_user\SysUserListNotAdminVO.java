package com.my.college.controller.vo.sys_user;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 非管理员
 *
 * @date 2024-05-20
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SysUserListNotAdminVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 用户id
     * @mock U001
     */
    private String userId;

    /**
     * 真实姓名
     * @mock 赵一铭
     */
    private String realName;

}
