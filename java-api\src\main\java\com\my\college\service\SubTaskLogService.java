package com.my.college.service;

import java.util.List;

import org.springframework.boot.logging.LogLevel;

import com.baomidou.mybatisplus.extension.service.IService;
import com.my.college.controller.vo.sub_task_log.SubTaskLogDetailVO;
import com.my.college.mybatis.entity.SubTaskLog;

/**
 * 子任务日志表 服务类
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
public interface SubTaskLogService extends IService<SubTaskLog> {

	/**
	 * 通过subTaskId查询所有日志，倒序
	 * @param subTaskId 子任务ID
	 * @return 日志列表
	 */
	List<SubTaskLogDetailVO> list(String subTaskId);

	/**
	 * 保存日志
	 * @param level
	 * @param content
	 */
	void save(LogLevel level, String content);

}
