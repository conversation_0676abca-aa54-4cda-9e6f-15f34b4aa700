package com.my.college.forest.markdown.dto;

import javax.validation.constraints.NotBlank;

import com.alibaba.fastjson2.annotation.JSONField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor @AllArgsConstructor @Builder @Data 
public class MarkdownDTO {

	/**
	 * 超时
	 */
	@NotBlank
	@JSONField(name = "url", ordinal = 1)
	String url;
	
	/**
	 * 抓取超时(秒)
	 */
	@JSONField(name = "timeout", ordinal = 2)
	Integer timeout;
	
	
//	/**
//	 * 是否保留link
//	 */
//    @JSONField(name = "keep_link", ordinal = 3)
//    Boolean keep_link;
//
//    /**
//     * 无头浏览器页面渲染延时(秒)
//     */
//    @JSONField(name = "render_delay", ordinal = 4)
//    Integer render_delay;


}