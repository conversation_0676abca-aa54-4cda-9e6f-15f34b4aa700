-- 关闭外键约束检查
SET REFERENTIAL_INTEGRITY FALSE;

-- ----------------------------
-- Table structure for address_book
-- ----------------------------
DROP TABLE IF EXISTS address_book;
CREATE TABLE address_book (
  id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  school_id INT NOT NULL,
  department VARCHAR(128),
  contact_name VARCHAR(128) NOT NULL,
  position VARCHAR(128),
  email VARCHAR(128),
  phone VARCHAR(32),
  url VARCHAR(1024),
  update_time TIMESTAMP NOT NULL,
  manual_flag BOOLEAN DEFAULT FALSE
);

COMMENT ON TABLE address_book IS '通讯录表';
COMMENT ON COLUMN address_book.id IS '主键ID';
COMMENT ON COLUMN address_book.school_id IS '学校ID';
COMMENT ON COLUMN address_book.department IS '部门';
COMMENT ON COLUMN address_book.contact_name IS '联系人姓名';
COMMENT ON COLUMN address_book.position IS '职位';
COMMENT ON COLUMN address_book.email IS '邮箱';
COMMENT ON COLUMN address_book.phone IS '电话';
COMMENT ON COLUMN address_book.url IS '数据来源url';
COMMENT ON COLUMN address_book.update_time IS '更新时间';
COMMENT ON COLUMN address_book.manual_flag IS '是否手动添加(0-否,1-是)';

CREATE INDEX idx_addr_book_school_id ON address_book(school_id);
CREATE INDEX idx_addr_book_contact_name ON address_book(contact_name);
CREATE INDEX idx_addr_book_email ON address_book(email);
CREATE INDEX idx_addr_book_update_time ON address_book(update_time);

-- ----------------------------
-- Table structure for province
-- ----------------------------
DROP TABLE IF EXISTS province;
CREATE TABLE province (
  province_id CHAR(2) NOT NULL PRIMARY KEY,
  english_name VARCHAR(50) NOT NULL,
  chinese_name VARCHAR(50) NOT NULL
);

COMMENT ON TABLE province IS '美国50个州州名';
COMMENT ON COLUMN province.province_id IS '字母缩写';
COMMENT ON COLUMN province.english_name IS '英文名称';
COMMENT ON COLUMN province.chinese_name IS '中文名称';

-- ----------------------------
-- Table structure for school
-- ----------------------------
DROP TABLE IF EXISTS school;
CREATE TABLE school (
  school_id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  province_id CHAR(2),
  chinese_name VARCHAR(255) NOT NULL,
  english_name VARCHAR(255),
  website VARCHAR(255) NOT NULL,
  remark VARCHAR(512),
  update_time TIMESTAMP
);

COMMENT ON TABLE school IS '学校表';
COMMENT ON COLUMN school.school_id IS '主键ID';
COMMENT ON COLUMN school.province_id IS '美国州字母缩写';
COMMENT ON COLUMN school.chinese_name IS '中文名称';
COMMENT ON COLUMN school.english_name IS '英文名称';
COMMENT ON COLUMN school.website IS '网址';
COMMENT ON COLUMN school.remark IS '备注';
COMMENT ON COLUMN school.update_time IS '更新时间';

CREATE UNIQUE INDEX idx_school_chinese_name ON school(chinese_name);
CREATE UNIQUE INDEX idx_school_website ON school(website);
CREATE UNIQUE INDEX idx_school_english_name ON school(english_name);

-- ----------------------------
-- Table structure for sub_task_log
-- ----------------------------
DROP TABLE IF EXISTS sub_task_log;
CREATE TABLE sub_task_log (
  log_id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  sub_task_id VARCHAR(32) NOT NULL,
  type VARCHAR(8) NOT NULL,
  content CLOB NOT NULL,
  create_time TIMESTAMP NOT NULL
);

COMMENT ON TABLE sub_task_log IS '子任务日志表';
COMMENT ON COLUMN sub_task_log.log_id IS '自增日志ID';
COMMENT ON COLUMN sub_task_log.sub_task_id IS '关联的任务编号';
COMMENT ON COLUMN sub_task_log.type IS '日志类型(ERROR/WARNING/SUCCESS/INFO)';
COMMENT ON COLUMN sub_task_log.content IS '日志详细内容';
COMMENT ON COLUMN sub_task_log.create_time IS '创建时间';

CREATE INDEX idx_sub_task_log_task_id ON sub_task_log(sub_task_id);

-- ----------------------------
-- Table structure for sub_task_record
-- ----------------------------
DROP TABLE IF EXISTS sub_task_record;
CREATE TABLE sub_task_record (
  sub_task_id VARCHAR(32) NOT NULL PRIMARY KEY,
  task_id VARCHAR(32) NOT NULL,
  start_time TIMESTAMP NOT NULL,
  end_time TIMESTAMP,
  finish_time TIMESTAMP,
  url CLOB NOT NULL,
  markdown_request CLOB,
  markdown_response CLOB,
  queue_id VARCHAR(64),
  ai_request CLOB,
  ai_response CLOB,
  ai_content CLOB,
  ai_reasoning CLOB,
  ai_token_usage VARCHAR(128),
  status VARCHAR(8) NOT NULL,
  fail_remark CLOB,
  create_time TIMESTAMP NOT NULL,
  update_time TIMESTAMP
);

COMMENT ON TABLE sub_task_record IS '子任务记录表';
COMMENT ON COLUMN sub_task_record.sub_task_id IS '子任务编号';
COMMENT ON COLUMN sub_task_record.task_id IS '主任务编号';
COMMENT ON COLUMN sub_task_record.start_time IS '子任务开始时间';
COMMENT ON COLUMN sub_task_record.end_time IS '子任务结束时间';
COMMENT ON COLUMN sub_task_record.finish_time IS '子任务完成时间';
COMMENT ON COLUMN sub_task_record.url IS '网址';
COMMENT ON COLUMN sub_task_record.markdown_request IS 'markdown请求参数';
COMMENT ON COLUMN sub_task_record.markdown_response IS 'markdown响应内容';
COMMENT ON COLUMN sub_task_record.queue_id IS '队列id';
COMMENT ON COLUMN sub_task_record.ai_request IS 'ai请求参数';
COMMENT ON COLUMN sub_task_record.ai_response IS 'ai响应内容';
COMMENT ON COLUMN sub_task_record.ai_content IS 'ai业务数据';
COMMENT ON COLUMN sub_task_record.ai_reasoning IS 'ai推理详情';
COMMENT ON COLUMN sub_task_record.ai_token_usage IS 'ai-token消耗情况';
COMMENT ON COLUMN sub_task_record.status IS '子任务状态(RUN, FAIL, SUCCESS, REBORN)';
COMMENT ON COLUMN sub_task_record.fail_remark IS '子任务失败备注';
COMMENT ON COLUMN sub_task_record.create_time IS '创建时间';
COMMENT ON COLUMN sub_task_record.update_time IS '更新时间';

CREATE INDEX idx_sub_task_record_status ON sub_task_record(status);
CREATE INDEX idx_sub_task_record_task_id ON sub_task_record(task_id);

-- ----------------------------
-- Table structure for sys_param
-- ----------------------------
DROP TABLE IF EXISTS sys_param;
CREATE TABLE sys_param (
  k VARCHAR(64) NOT NULL PRIMARY KEY,
  v CLOB NOT NULL
);

COMMENT ON TABLE sys_param IS '系统参数配置表';
COMMENT ON COLUMN sys_param.k IS '键';
COMMENT ON COLUMN sys_param.v IS '值';

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS sys_user;
CREATE TABLE sys_user (
  username VARCHAR(32) NOT NULL PRIMARY KEY,
  password VARCHAR(32) NOT NULL,
  admin_flag BOOLEAN NOT NULL,
  remark VARCHAR(255)
);

COMMENT ON TABLE sys_user IS '系统用户';
COMMENT ON COLUMN sys_user.username IS '用户名';
COMMENT ON COLUMN sys_user.password IS '密码';
COMMENT ON COLUMN sys_user.admin_flag IS '是否管理员';
COMMENT ON COLUMN sys_user.remark IS '备注';

CREATE UNIQUE INDEX uq_sys_user_username ON sys_user(username);

-- ----------------------------
-- Table structure for task_log
-- ----------------------------
DROP TABLE IF EXISTS task_log;
CREATE TABLE task_log (
  log_id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  task_id VARCHAR(32) NOT NULL,
  type VARCHAR(8) NOT NULL,
  content CLOB NOT NULL,
  create_time TIMESTAMP NOT NULL
);

COMMENT ON TABLE task_log IS '任务日志表';
COMMENT ON COLUMN task_log.log_id IS '自增日志ID';
COMMENT ON COLUMN task_log.task_id IS '关联的任务编号';
COMMENT ON COLUMN task_log.type IS '日志类型(ERROR/WARNING/SUCCESS/INFO)';
COMMENT ON COLUMN task_log.content IS '日志详细内容';
COMMENT ON COLUMN task_log.create_time IS '创建时间';

CREATE INDEX idx_task_log_task_id ON task_log(task_id);

-- ----------------------------
-- Table structure for task_record
-- ----------------------------
DROP TABLE IF EXISTS task_record;
CREATE TABLE task_record (
  task_id VARCHAR(32) NOT NULL PRIMARY KEY,
  batch_number BIGINT NOT NULL,
  school_id INT NOT NULL,
  start_time TIMESTAMP NOT NULL,
  end_time TIMESTAMP,
  finish_time TIMESTAMP,
  request CLOB,
  response CLOB,
  status VARCHAR(8) NOT NULL,
  fail_remark CLOB,
  create_time TIMESTAMP NOT NULL,
  update_time TIMESTAMP
);

COMMENT ON TABLE task_record IS '任务记录表';
COMMENT ON COLUMN task_record.task_id IS '任务编号';
COMMENT ON COLUMN task_record.batch_number IS '批次号';
COMMENT ON COLUMN task_record.school_id IS '学校id';
COMMENT ON COLUMN task_record.start_time IS '任务开始时间';
COMMENT ON COLUMN task_record.end_time IS '任务结束时间';
COMMENT ON COLUMN task_record.finish_time IS '任务完成时间';
COMMENT ON COLUMN task_record.request IS '请求参数';
COMMENT ON COLUMN task_record.response IS '响应内容';
COMMENT ON COLUMN task_record.status IS '任务状态(RUN, FAIL, SUCCESS, REBORN)';
COMMENT ON COLUMN task_record.fail_remark IS '任务失败备注';
COMMENT ON COLUMN task_record.create_time IS '创建时间';
COMMENT ON COLUMN task_record.update_time IS '更新时间';

CREATE INDEX idx_task_record_status ON task_record(status);
CREATE INDEX idx_task_record_school_id ON task_record(school_id);
CREATE INDEX idx_task_record_batch_number ON task_record(batch_number);

-- 启用外键约束检查
SET REFERENTIAL_INTEGRITY TRUE;


INSERT INTO `province` VALUES ('AK', 'Alaska', '阿拉斯加州');
INSERT INTO `province` VALUES ('AL', 'Alabama', '阿拉巴马州');
INSERT INTO `province` VALUES ('AR', 'Arkansas', '阿肯色州');
INSERT INTO `province` VALUES ('AZ', 'Arizona', '亚利桑那州');
INSERT INTO `province` VALUES ('CA', 'California', '加利福尼亚州');
INSERT INTO `province` VALUES ('CO', 'Colorado', '科罗拉多州');
INSERT INTO `province` VALUES ('CT', 'Connecticut', '康涅狄格州');
INSERT INTO `province` VALUES ('DE', 'Delaware', '特拉华州');
INSERT INTO `province` VALUES ('FL', 'Florida', '佛罗里达州');
INSERT INTO `province` VALUES ('GA', 'Georgia', '佐治亚州');
INSERT INTO `province` VALUES ('HI', 'Hawaii', '夏威夷州');
INSERT INTO `province` VALUES ('IA', 'Iowa', '艾奥瓦州');
INSERT INTO `province` VALUES ('ID', 'Idaho', '爱达荷州');
INSERT INTO `province` VALUES ('IL', 'Illinois', '伊利诺伊州');
INSERT INTO `province` VALUES ('IN', 'Indiana', '印第安纳州');
INSERT INTO `province` VALUES ('KS', 'Kansas', '堪萨斯州');
INSERT INTO `province` VALUES ('KY', 'Kentucky', '肯塔基州');
INSERT INTO `province` VALUES ('LA', 'Louisiana', '路易斯安那州');
INSERT INTO `province` VALUES ('MA', 'Massachusetts', '马萨诸塞州');
INSERT INTO `province` VALUES ('MD', 'Maryland', '马里兰州');
INSERT INTO `province` VALUES ('ME', 'Maine', '缅因州');
INSERT INTO `province` VALUES ('MI', 'Michigan', '密歇根州');
INSERT INTO `province` VALUES ('MN', 'Minnesota', '明尼苏达州');
INSERT INTO `province` VALUES ('MO', 'Missouri', '密苏里州');
INSERT INTO `province` VALUES ('MS', 'Mississippi', '密西西比州');
INSERT INTO `province` VALUES ('MT', 'Montana', '蒙大拿州');
INSERT INTO `province` VALUES ('NC', 'North Carolina', '北卡罗来纳州');
INSERT INTO `province` VALUES ('ND', 'North Dakota', '北达科他州');
INSERT INTO `province` VALUES ('NE', 'Nebraska', '内布拉斯加州');
INSERT INTO `province` VALUES ('NH', 'New Hampshire', '新罕布什尔州');
INSERT INTO `province` VALUES ('NJ', 'New Jersey', '新泽西州');
INSERT INTO `province` VALUES ('NM', 'New Mexico', '新墨西哥州');
INSERT INTO `province` VALUES ('NV', 'Nevada', '内华达州');
INSERT INTO `province` VALUES ('NY', 'New York', '纽约州');
INSERT INTO `province` VALUES ('OH', 'Ohio', '俄亥俄州');
INSERT INTO `province` VALUES ('OK', 'Oklahoma', '俄克拉荷马州');
INSERT INTO `province` VALUES ('OR', 'Oregon', '俄勒冈州');
INSERT INTO `province` VALUES ('PA', 'Pennsylvania', '宾夕法尼亚州');
INSERT INTO `province` VALUES ('RI', 'Rhode Island', '罗得岛州');
INSERT INTO `province` VALUES ('SC', 'South Carolina', '南卡罗来纳州');
INSERT INTO `province` VALUES ('SD', 'South Dakota', '南达科他州');
INSERT INTO `province` VALUES ('TN', 'Tennessee', '田纳西州');
INSERT INTO `province` VALUES ('TX', 'Texas', '得克萨斯州');
INSERT INTO `province` VALUES ('UT', 'Utah', '犹他州');
INSERT INTO `province` VALUES ('VA', 'Virginia', '弗吉尼亚州');
INSERT INTO `province` VALUES ('VT', 'Vermont', '佛蒙特州');
INSERT INTO `province` VALUES ('WA', 'Washington', '华盛顿州');
INSERT INTO `province` VALUES ('WI', 'Wisconsin', '威斯康星州');
INSERT INTO `province` VALUES ('WV', 'West Virginia', '西弗吉尼亚州');
INSERT INTO `province` VALUES ('WY', 'Wyoming', '怀俄明州');
 
INSERT INTO `school` VALUES (26, 'CA', '哈佛大学', 'Harvard University', 'https://www.harvard.edu', '常春藤盟校，全美综合排名第一', '2025-05-24 00:33:23');
INSERT INTO `school` VALUES (27, 'CA', '麻省理工学院', 'Massachusetts Institute of Technology (MIT)', 'https://www.mit.edu', '世界顶尖理工学院，工程与计算机科学领先', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (31, 'CA', '普林斯顿大学', 'Princeton University', 'https://www.princeton.edu', '常春藤盟校，理论科学与工程学科突出', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (32, 'CA', '哥伦比亚大学', 'Columbia University', 'https://www.columbia.edu', '常春藤盟校，位于纽约曼哈顿，新闻学院闻名', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (33, 'CA', '芝加哥大学', 'University of Chicago', 'https://www.uchicago.edu', '私立研究型大学，经济学“芝加哥学派”发源地', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (34, 'CA', '宾夕法尼亚大学', 'University of Pennsylvania (UPenn)', 'https://www.upenn.edu', '常春藤盟校，沃顿商学院全球顶尖', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (37, 'CA', '约翰霍普金斯大学', 'Johns Hopkins University (JHU)', 'https://www.jhu.edu', '医学与公共卫生领域全球领先', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (39, 'CA', '西北大学', 'Northwestern University', 'https://www.northwestern.edu', '私立研究型大学，传媒学院与法学院突出', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (40, 'CA', '卡内基梅隆大学', 'Carnegie Mellon University (CMU)', 'https://www.cmu.edu', '计算机科学与人工智能顶尖，机器人专业全球第一', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (41, 'CA', '密歇根大学安娜堡分校', 'University of Michigan, Ann Arbor', 'https://www.umich.edu', '公立旗舰大学，工程与商科实力强劲', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (42, 'CA', '佐治亚理工学院', 'Georgia Institute of Technology (Georgia Tech)', 'https://www.gatech.edu', '公立理工强校，与MIT、加州理工并称“美国三大理工学院”', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (43, 'CA', '华盛顿大学西雅图分校', 'University of Washington, Seattle', 'https://www.washington.edu', '公立研究型大学，计算机科学与医学见长', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (44, 'CA', '加州大学洛杉矶分校', 'University of California, Los Angeles (UCLA)', 'https://www.ucla.edu', '公立顶尖综合大学，学术与体育氛围浓厚', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (45, 'CA', '南加州大学', 'University of Southern California (USC)', 'https://www.usc.edu', '私立大学，电影艺术学院（SCA）全球第一', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (46, 'CA', '纽约大学', 'New York University (NYU)', 'https://www.nyu.edu', '位于纽约市中心，商科、艺术与人文社科知名', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (47, 'CA', '波士顿大学', 'Boston University (BU)', 'https://www.bu.edu', '私立研究型大学，传媒与工程学科突出', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (48, 'CA', '北卡罗来纳大学教堂山分校', 'University of North Carolina at Chapel Hill (UNC Chapel Hill)', 'https://www.unc.edu', '公立常春藤，医学与商学顶尖', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (49, 'CA', '德克萨斯大学奥斯汀分校', 'University of Texas at Austin (UT Austin)', 'https://www.utexas.edu', '公立研究型大学，工程与计算机科学强校', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (50, 'CA', '威斯康星大学麦迪逊分校', 'University of Wisconsin-Madison', 'https://www.wisc.edu', '公立旗舰大学，农业与生命科学领域领先', '2025-05-24 00:22:31');
INSERT INTO `school` VALUES (14374, 'CA', '斯坦福大学', 'Stanford University', 'https://www.stanford.edu/', NULL, '2025-05-25 17:08:23');
INSERT INTO `school` VALUES (14375, 'CA', '加州大学伯克利分校', 'University of California, Berkeley', 'https://www.berkeley.edu/', NULL, '2025-05-25 17:08:23');
INSERT INTO `school` VALUES (14376, 'CA', '加州理工学院', 'California Institute of Technology', 'https://www.caltech.edu/', NULL, '2025-05-25 17:08:23');
INSERT INTO `school` VALUES (14377, 'CA', '耶鲁大学', 'Yale University', 'https://www.yale.edu/', NULL, '2025-05-25 17:08:23');
INSERT INTO `school` VALUES (14378, 'CA', '康奈尔大学', 'Cornell University', 'https://www.cornell.edu/', NULL, '2025-05-25 17:08:23');
INSERT INTO `school` VALUES (14379, 'CA', '约翰·霍普金斯大学', 'Johns Hopkins University', 'https://www.jhu.edu/', NULL, '2025-05-25 17:08:23');
  
INSERT INTO `sys_param` VALUES ('aiApiKey', 'sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud');
INSERT INTO `sys_param` VALUES ('aiModel', 'Qwen/QwQ-32B');
INSERT INTO `sys_param` VALUES ('aiSystemPrompt', '-');
INSERT INTO `sys_param` VALUES ('aiTimeout', '300');
INSERT INTO `sys_param` VALUES ('aiUserPrompt', 'Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"Depar tment\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"Position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"Email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"Phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.');
INSERT INTO `sys_param` VALUES ('batchNumber', '66');
INSERT INTO `sys_param` VALUES ('googleApiKey', 'AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc');
INSERT INTO `sys_param` VALUES ('googleEngineID', '66818a77d1c67403b');
INSERT INTO `sys_param` VALUES ('googleKeyword', 'site:学校网址 intext:(\"study abroad director\" OR \"associate director\" OR \"study abroad coordinator\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") intitle:(\"staff\" OR \"contact\" OR \"directory\" OR \"people\") intext:(\"office of international education\" OR \"OIE\" OR \"study abroad office\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf');
INSERT INTO `sys_param` VALUES ('markdownTimeout', '10');
 
 
INSERT INTO `sys_user` VALUES ('admin', 'e10adc3949ba59abbe56e057f20f883e', 1, NULL);
INSERT INTO `sys_user` VALUES ('liwenchao', 'e10adc3949ba59abbe56e057f20f883e', 1, '');
INSERT INTO `sys_user` VALUES ('test', 'e10adc3949ba59abbe56e057f20f883e', 0, '测试 - 非管理员用户');
 
