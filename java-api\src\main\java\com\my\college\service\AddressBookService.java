package com.my.college.service;

import com.my.college.mybatis.entity.AddressBook;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.college.controller.dto.address_book.AddressBookInsertDTO;
import com.my.college.controller.dto.address_book.AddressBookPageDTO;
import com.my.college.controller.dto.address_book.AddressBookUpdateDTO;
import com.my.college.controller.dto.address_book.AddressBookUploadDTO;
import com.my.college.controller.dto.address_book.AddressBookDeleteDTO;
import com.my.college.controller.vo.address_book.AddressBookDetailVO;
import com.my.college.controller.vo.address_book.AddressBookPageVO;

/**
 * 通讯录表 服务类
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
public interface AddressBookService extends IService<AddressBook> {

	/**
	 * 新增
	 */
	void insert(AddressBookInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(AddressBookUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	AddressBookDetailVO detail(Integer id);

	/**
	 * 分页
	 */
	Page<AddressBookPageVO> page(AddressBookPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(AddressBookDeleteDTO deleteDTO);

	/**
	 * count
	 * @param schoolIdList
	 * @return
	 */
	Integer countBySchoolId(List<Integer> schoolIdList);

	/**
	 * delete
	 * @param schoolIdList
	 */
	void deleteBySchoolId(List<Integer> schoolIdList);

	/**
	 * 批量新增联系人
	 * @param validList
	 */
	void batchInsert(Set<AddressBookUploadDTO> validList);	

	/**
	 * 根据学校ID和联系人姓名查询联系人
	 * @param schoolId 学校ID
	 * @param contactName 联系人姓名
	 * @return 联系人实体
	 */
	AddressBook getBySchoolIdAndContactName(Integer schoolId, String contactName);

	/**
	 * ai内容批量保存到addressBook表
	 * @param aiContent
	 */
	void batchInsert(String aiContent);


}
