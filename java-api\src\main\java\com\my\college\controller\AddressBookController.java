package com.my.college.controller;


import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.my.college.auth.AuthConstant;
import com.my.college.controller.dto.address_book.AddressBookDeleteDTO;
import com.my.college.controller.dto.address_book.AddressBookDownloadDTO;
import com.my.college.controller.dto.address_book.AddressBookInsertDTO;
import com.my.college.controller.dto.address_book.AddressBookPageDTO;
import com.my.college.controller.dto.address_book.AddressBookUpdateDTO;
import com.my.college.controller.dto.address_book.AddressBookUploadDTO;
import com.my.college.controller.vo.StdResp;
import com.my.college.controller.vo.UploadResultVO;
import com.my.college.controller.vo.address_book.AddressBookDetailVO;
import com.my.college.controller.vo.address_book.AddressBookPageVO;
import com.my.college.exception.BusinessException;
import com.my.college.mybatis.entity.SysUser;
import com.my.college.service.AddressBookService;
import com.my.college.service.SysUserService;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 通讯录表 
 * 
 * <AUTHOR>
 * @date 2025-05-23
 */
@RestController
@RequestMapping("/api/address-book")
@RequiredArgsConstructor
@Slf4j
public class AddressBookController {

    private final AddressBookService addressBookService;
    private final SysUserService sysUserService;
    

    /**
    * 新增
    */
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody AddressBookInsertDTO insertDTO) {
    	this.addressBookService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @PutMapping("")
    public StdResp<?> update(@Valid @RequestBody AddressBookUpdateDTO updateDTO) {
    	this.addressBookService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<AddressBookDetailVO> detail(@PathVariable Integer id) {
    	return StdResp.success(this.addressBookService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<AddressBookPageVO>> page(AddressBookPageDTO pageDTO) {
        Page<AddressBookPageVO> page = this.addressBookService.page(pageDTO);
        return StdResp.success(page);
    }
    
    /**
    * 批量删除(物理删除)
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody AddressBookDeleteDTO deleteDTO) {
    	this.addressBookService.delete(deleteDTO);
		return StdResp.success();
    }
    

    /**
	 * 上传并验证
	 * @param file
	 */
	@PostMapping("upload")
	public StdResp<UploadResultVO> upload(MultipartFile file) throws IOException {
		UploadResultVO result = new UploadResultVO(); 
		PageReadListener<AddressBookUploadDTO> readListener = new PageReadListener<AddressBookUploadDTO>(sourceList -> {
			Set<AddressBookUploadDTO> validList = Sets.newHashSet();
			List<String> errList = Lists.newLinkedList();
			int i = 0;
			for (AddressBookUploadDTO dto : sourceList) {
				String lineErr = dto.validate(++i);
				if (StrUtil.isBlank(lineErr)) {
					validList.add(dto);
				} else {
					errList.add(lineErr);
				}
			}
			int validCnt = validList.size();
			int totalCnt = sourceList.size();
			int invalidCnt = totalCnt - validCnt;
			UploadResultVO validating = UploadResultVO.builder()
					.validCnt(validCnt)
					.invalidCnt(invalidCnt)
					.totalCnt(totalCnt)
					.valid(validCnt == totalCnt)
					.errList(errList)
					.build();
			log.info("【联系人excel】上传成功。校验结果：valid[{}] + invalid[{}] = total[{}]", validCnt, invalidCnt, totalCnt);
			if (!validList.isEmpty()) {
				this.addressBookService.batchInsert(validList);
			}
			BeanUtil.copyProperties(validating, result);
		});
		BusinessException.when(file == null, "上传文件不能为空");
		try (ExcelReader read = EasyExcel.read(file.getInputStream(), AddressBookUploadDTO.class, readListener).build()){
			read.read(EasyExcel.readSheet(0).build());
		}
		return StdResp.success(result);
	}
    
    /**
     * 导出excel
     */
    @GetMapping("/download")
    public void download(HttpServletResponse response, @Valid AddressBookDownloadDTO downloadDTO) throws IOException {
    	// 通过token查询用户
    	String token = downloadDTO.getToken(); 
    	String userId = (String) StpUtil.getLoginIdByToken(token);
    	SysUser user = this.sysUserService.getById(userId);
        StpUtil.getTokenSession().set(AuthConstant.TOKEN_SESSION_USER, user);
    	
    	// 下载查询到的全部数据
    	downloadDTO.setSize(10000000L);
    	Page<AddressBookPageVO> page = this.addressBookService.page(downloadDTO);
		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		response.setCharacterEncoding("utf-8");
		// URLEncoder.encode可以防止中文乱码
		String timeStamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
		String fileName = URLEncoder.encode("联系人清单" + timeStamp, "UTF-8").replaceAll("\\+", "%20");
		response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
		EasyExcel.write(response.getOutputStream(), AddressBookPageVO.class).sheet("联系人").doWrite(page.getRecords());
    }
}
