package com.my.college.task;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import org.springframework.boot.logging.LogLevel;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;
import com.my.college.controller.vo.sys_param.SysParamVO;
import com.my.college.exception.BusinessException;
import com.my.college.forest.google.GoogleClient;
import com.my.college.forest.google.dto.CustomSearchDTO;
import com.my.college.forest.google.onerror.CustomSearchOnError;
import com.my.college.forest.google.vo.CustomSearchVO;
import com.my.college.forest.google.vo.CustomSearchVO.Items;
import com.my.college.mybatis.entity.School;
import com.my.college.service.SysParamService;
import com.my.college.service.TaskLogService;
import com.my.college.service.TaskRecordService;
import com.my.college.task.subtask.SubTaskEntrance;
import com.my.college.util.CollegeWebsiteUtil;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务处理（调用谷歌）
 * <AUTHOR>
 *
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class TaskProcess {

	private static final String TPL_SCHOOL_WEBSITE = "学校网址";
	private final GoogleClient googleClient;
	private final TaskRecordService taskRecordService;
	private final TaskLogService taskLogService;
	private final SubTaskEntrance subTaskEntrance;

	
	/**
	 * 执行任务处理流程
	 * @param batchNumber 
	 * @param school 
	 * @param sysParam
	 */
	public void process(Integer batchNumber, School school, SysParamVO sysParam) {
		String taskId = IdUtil.getSnowflakeNextIdStr();
		TaskContext.set(batchNumber, taskId, school.getSchoolId());
		
		// 创建任务(状态为RUN)
		this.taskRecordService.insert();
		
		// [请求.1] 构造请求参数
		CustomSearchDTO customSearchDTO = this.buildParam(school.getWebsite());
		String request = JSON.toJSONString(customSearchDTO);
		this.taskRecordService.updateRequest(request);
		
		// 异步请求
		this.asyncProcess(customSearchDTO);
	}
	
	/**
	 * 异步请求
	 * @param customSearchDTO
	 */
	private void asyncProcess(CustomSearchDTO customSearchDTO) {
		String currentTaskId = TaskContext.getTaskId();
		Integer currentBatchNumber = TaskContext.getBatchNumber();
		Integer currentSchoolId = TaskContext.getSchoolId();
		
		// 异步调用接口
		CompletableFuture.supplyAsync(() -> {
			return this.sendReqeust(customSearchDTO, currentBatchNumber, currentTaskId, currentSchoolId);
		}).thenAccept(response -> {
			this.onSuccess(response, currentBatchNumber, currentTaskId, currentSchoolId);
		}).exceptionally(e -> {
			this.onError(e, currentBatchNumber, currentTaskId, currentSchoolId);
			return null;
		});
	}

	/**
	 * [请求.1]构造请求参数
	 */
	private CustomSearchDTO buildParam(String schoolUrl) {
		SysParamVO sysParam = SpringUtil.getBean(SysParamService.class).get();
		String key = sysParam.getGoogleApiKey();
		String cx = sysParam.getGoogleEngineID();
		String mainDomain = CollegeWebsiteUtil.extractMainDomain(schoolUrl);
		String q = sysParam.getGoogleKeyword().replaceAll(TPL_SCHOOL_WEBSITE, mainDomain);
		CustomSearchDTO customSearchDTO = new CustomSearchDTO(key, cx, q);
		return customSearchDTO;
	}
	
	/**
	 * [请求.2]发送请求, 等待返回
	 */
	private CustomSearchVO sendReqeust(CustomSearchDTO customSearchDTO, 
							Integer currentBatchNumber, String currentTaskId, Integer currentSchoolId) {
		// 沿用旧的上下文
		TaskContext.set(currentBatchNumber, currentTaskId, currentSchoolId);
		try {
			CustomSearchOnError onError = new CustomSearchOnError();
			CustomSearchVO vo = this.googleClient.customSearch(customSearchDTO, onError);
			this.taskLogService.save(LogLevel.INFO, "接口调用成功, 等待响应");
			BusinessException.when(vo == null, onError.getErrMsg());
			return vo;
		} finally {
			TaskContext.remove();
		}
	}
	
	/**
	 * [请求.3]接口响应成功
	 */
	private void onSuccess(CustomSearchVO customSearchVO,
						Integer currentBatchNumber, String currentTaskId, Integer currentSchoolId) {
		// 沿用旧的上下文
		TaskContext.set(currentBatchNumber, currentTaskId, currentSchoolId);
		try {
			// 保存响应内容
			List<Items> items = customSearchVO.getItems();
			String response = JSON.toJSONString(items);
			this.taskRecordService.updateResponse(response);
			
			// 无数据则任务完成
			if (CollectionUtil.isEmpty(items)) {
				this.taskRecordService.succeed();
			} else {
				//  批量子任务
				List<String> urlList = items.stream().map(t -> t.getLink()).collect(Collectors.toList());
				this.subTaskEntrance.batchCreat(currentTaskId, urlList);
			}
		} catch (Exception e) {
			log.error(e.getMessage());
			this.taskRecordService.fail("google搜索结果运算失败: " + e.getMessage());
		} finally {
			TaskContext.remove();
		}
	}
	
	/**
	 * [请求.4]接口响应异常
	 */
	private void onError(Throwable e,
					Integer currentBatchNumber, String currentTaskId, Integer currentSchoolId) {
		// 沿用旧的上下文
		TaskContext.set(currentBatchNumber, currentTaskId, currentSchoolId);
		try {
			// 更新任务状态为失败
			this.taskRecordService.fail("google接口调用失败: " + e.getMessage());
		} finally {
			TaskContext.remove();
		}
	}
}
