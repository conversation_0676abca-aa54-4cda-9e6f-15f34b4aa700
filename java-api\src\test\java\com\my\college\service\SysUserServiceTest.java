package com.my.college.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import cn.hutool.crypto.digest.DigestUtil;
import lombok.extern.slf4j.Slf4j;

//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//@RunWith(SpringRunner.class)
@Slf4j
class SysUserServiceTest {
	
	@Autowired
	SysUserService sysUserService;

	@Test
	void testGet() {
//		fail("Not yet implemented");
	}
	
	@Test
	void md5() {
		String password = "admin";
		String md5Password = DigestUtil.md5Hex(password);
		log.info("md5({}) => {}", password, md5Password);
	}
	
}
