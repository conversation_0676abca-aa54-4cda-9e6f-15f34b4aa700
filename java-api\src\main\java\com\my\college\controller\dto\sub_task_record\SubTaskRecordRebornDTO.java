package com.my.college.controller.dto.sub_task_record;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 子任务重生
 * <AUTHOR>
 *
 */
@Data
public class SubTaskRecordRebornDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 子任务编号 
     */
	@NotNull(message="subTaskId不能为空")
    private String subTaskId;
	
}
