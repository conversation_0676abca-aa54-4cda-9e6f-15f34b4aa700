package com.my.college.forest.google;

import com.dtflys.forest.annotation.Address;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Query;
import com.dtflys.forest.callback.OnError;
import com.my.college.forest.google.address.GoogleAddress;
import com.my.college.forest.google.dto.CustomSearchDTO;
import com.my.college.forest.google.vo.CustomSearchVO;

/**
 * google接口
 * 
 * <AUTHOR>
 */
@Address(source = GoogleAddress.class)
public interface GoogleClient {

    
	/**
	 * 搜索
	 * @return 搜索结果
	 */
	@Get(url = "/customsearch/v1", 
		readTimeout = 5 * 1000, 
		connectTimeout =  5 * 1000)
	CustomSearchVO customSearch(@Query CustomSearchDTO customSearchDTO, OnError onError);
	
//    /**
//     * 搜索
//     * @param key  API密钥
//     * @param cx 搜索引擎ID
//     * @param q 搜索关键词
//     * @param start 开始位置
//     * @param num 每页数量
//     * @return 搜索结果
//     * @throws RuntimeException 当API调用失败时抛出异常
//     */
//    @Get(url = "/customsearch/v1", dataType = "json")
//    CustomSearchVO customSearch(@Query("key") String key,
//                            @Query("cx") String cx,
//                            @Query("q") String q,
//                            @Query("start") Integer start,
//                            @Query("num") Integer num,
//                            OnError onError);
    
} 