package com.my.college.task.subtask;

import java.util.List;
import java.util.concurrent.CompletableFuture;

import org.springframework.boot.logging.LogLevel;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;
import com.dtflys.forest.http.ForestRequest;
import com.google.common.collect.Lists;
import com.my.college.controller.vo.sys_param.SysParamVO;
import com.my.college.forest.siliconflow.SiliconflowClient;
import com.my.college.forest.siliconflow.dto.ChatCompletionsDTO;
import com.my.college.forest.siliconflow.dto.component.Message;
import com.my.college.forest.siliconflow.onerrror.SiliconflowOnError;
import com.my.college.forest.siliconflow.vo.ChatCompletionsVO;
import com.my.college.service.AddressBookService;
import com.my.college.service.SubTaskLogService;
import com.my.college.service.SubTaskRecordService;
import com.my.college.service.SysParamService;
import com.my.college.task.TaskContext;
import com.my.college.util.RegExpUtil;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * AI处理
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AIProcess {

	private final SiliconflowClient siliconflowClient;
	private final SubTaskLogService subTaskLogService;
	private final SubTaskRecordService subTaskRecordService;
	private final SysParamService sysParamService;
	private final AddressBookService addressBookService;
	
	
	/**
	 * 执行AI分析流程
	 * @param currentTaskId
	 * @param currentSubTaskId
	 * @param currentUrl
	 * @param markdown
	 */
	public void process(String currentTaskId, String currentSubTaskId, String currentUrl, String markdown) {
		// 沿用旧的上下文
		SubTaskContext.set(currentTaskId, currentSubTaskId, currentUrl);
		
		// [请求.1] 构造请求参数
		ChatCompletionsDTO chatCompletionsDTO = this.buildParam(markdown);
		this.subTaskRecordService.updateAIRequest(JSON.toJSONString(chatCompletionsDTO));

		// 异步请求
		this.asyncProcess(chatCompletionsDTO);
	}
	
	/**
	 * 异步请求
	 */
	private void asyncProcess(ChatCompletionsDTO chatCompletionsDTO) {
		String currentTaskId = SubTaskContext.getTaskId();
		String currentSubTaskId = SubTaskContext.getSubTaskId();
		String currentUrl = SubTaskContext.getUrl();
		
		// 异步调用接口
		CompletableFuture.supplyAsync(() -> {
			// 设置超时参数
			SysParamVO sysParam = this.sysParamService.get();
			String aiApiKey = sysParam.getAiApiKey();
			Integer aiTimeout = new Integer(sysParam.getAiTimeout());
			SiliconflowOnError onError = new SiliconflowOnError();
			ForestRequest<ChatCompletionsVO> request = this.siliconflowClient.chatCompletions(chatCompletionsDTO, aiApiKey, onError);
			request.setConnectTimeout(aiTimeout * 1000);
			request.setReadTimeout(aiTimeout * 1000);
			return this.sendReqeust(request, currentTaskId, currentSubTaskId, currentUrl);
		}).thenAccept(response -> {
			this.onSuccess(response, currentTaskId, currentSubTaskId, currentUrl);
		}).exceptionally(e -> {
			this.onError(e, currentTaskId, currentSubTaskId, currentUrl);
			return null;
		});
	}
	
	/**
	 * [请求.1]构造请求参数
	 */
	private ChatCompletionsDTO buildParam(String markdown) {
		// ai需要的参数
		SysParamVO sysParam = this.sysParamService.get();
		String model = sysParam.getAiModel();
		String aiUserPrompt = sysParam.getAiUserPrompt().replaceAll("\\$\\{content\\}", java.util.regex.Matcher.quoteReplacement(markdown));
		Double temperature = 0.01D;
		List<Message> messages = Lists.newArrayList(
			Message.user(aiUserPrompt)
		);
		return ChatCompletionsDTO.builder()
				.model(model)
				.messages(messages)
				.temperature(temperature)
				.build();
	}
	
	/**
	 * [请求.2]发送请求, 等待返回
	 */
	private ChatCompletionsVO sendReqeust(ForestRequest<ChatCompletionsVO> req, 
							String currentTaskId, String currentSubTaskId, String currentUrl) {
		// 沿用旧的上下文
		SubTaskContext.set(currentTaskId, currentSubTaskId, currentUrl);
		try {
			ChatCompletionsVO vo = req.execute(ChatCompletionsVO.class);
			this.subTaskLogService.save(LogLevel.INFO, "AI接口调用成功, 等待响应");
			return vo;
		} finally {
			TaskContext.remove();
		}
	}
	
	/**
	 * [请求.3]接口响应成功
	 */
	private void onSuccess(ChatCompletionsVO vo,
							String currentTaskId, String currentSubTaskId, String currentUrl) {
		// 沿用旧的上下文
		SubTaskContext.set(currentTaskId, currentSubTaskId, currentUrl);
		try {
			// 仅保存aiResponse
			this.subTaskRecordService.updateAIResponse(JSON.toJSONString(vo));
			
			// ai内容批量保存到addressBook表
			String aiContent = vo.getChoices().get(0).getMessage().getContent();
			if (StrUtil.isNotBlank(aiContent)) {
				if (aiContent.startsWith("```json") || aiContent.startsWith("```JSON")) {
					aiContent = RegExpUtil.getFirstMatch("(?s)```json(.+)```", aiContent);
				}
				this.addressBookService.batchInsert(aiContent);
			} else {
				log.warn("本次AI分析无联系人数据. url:{}", currentUrl);
			}
			
			// 保存ai响应的多个字段
			this.subTaskRecordService.updateAIResult(vo);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			this.subTaskRecordService.fail("AI结果保存失败: " + e.getMessage());
		} finally {
			TaskContext.remove();
		}
	}
	
	/**
	 * [请求.4]接口响应异常
	 */
	private void onError(Throwable e,
						String currentTaskId, String currentSubTaskId, String currentUrl) {
		// 沿用旧的上下文
		SubTaskContext.set(currentTaskId, currentSubTaskId, currentUrl);
		try {
			// 更新任务状态为失败
			this.subTaskRecordService.fail("AI接口调用失败: " + e.getMessage());
		} finally {
			TaskContext.remove();
		}
	}
	
}
