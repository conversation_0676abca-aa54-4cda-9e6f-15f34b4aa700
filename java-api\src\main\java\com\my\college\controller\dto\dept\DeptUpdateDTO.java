package com.my.college.controller.dto.dept;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 修改_部门表
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class DeptUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门
     */
    @NotBlank(message="dept不能为空")
    private String dept;

}
