package com.my.college.controller.vo.sub_task_log;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * 查看实体详情_子任务日志表
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class SubTaskLogPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增日志ID
     */
    private Integer logId;

    /**
     * 关联的任务编号
     */
    private String subTaskId;

    /**
     * 日志类型(ERROR/WARNING/SUCCESS/INFO)
     */
    private String type;

    /**
     * 日志详细内容
     */
    private String content;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
