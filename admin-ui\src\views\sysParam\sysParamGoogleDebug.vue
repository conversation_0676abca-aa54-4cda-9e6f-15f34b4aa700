<template>
  <el-card class="box-card">
    <!-- <div slot="header" class="clearfix">
      <span>Google搜索调试工具</span>
    </div> -->

    <el-form label-width="130px">
      <el-form-item label="学校">
        <div class="school-container">
          <el-select v-model="selectedSchool" placeholder="请选择学校" filterable clearable class="half-width"
            @change="onSchoolChange">
            <el-option v-for="school in schoolList" :key="school.schoolId" :label="school.chineseName" :value="school">
            </el-option>
          </el-select>
          <div v-if="selectedSchool && selectedSchool.website" class="school-website">
            学校网址：<el-link type="primary" :href="selectedSchool.website" target="_blank">{{ selectedSchool.website
              }}</el-link>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="职位列表">
        <div class="transfer-container-left">
          <el-transfer
            v-model="selectedPositions"
            :data="positionData"
            :titles="['待选', '已选择']"
            :button-texts="['', '']"
            :format="{
              noChecked: '${total}',
              hasChecked: '${checked}/${total}'
            }"
            @change="onPositionSelectionChange"
            filterable
            :filter-placeholder="'请输入职位名称'"
            class="wide-transfer"
            style="text-align: left; display: inline-block">
          </el-transfer>
        </div>
        <!--
          <div style="color: #909399; font-size: 12px; margin-top: 5px;">
            常用职位：
          <el-tag
            v-for="position in examplePositions"
            :key="position"
            size="small"
            style="margin: 2px; cursor: pointer;"
            @click="addPositionFromTag(position)"
          >
            {{ position }}
          </el-tag>
        </div>
        -->
      </el-form-item>

      <el-form-item label="谷歌关键词">
        <el-input type="textarea" v-model="googleSearchQuery" placeholder="Google搜索语句" :rows="4" readonly class="half-width">
        </el-input>
        <el-button type="primary" size="small" @click="openGoogleSearch" :disabled="!googleSearchQuery">
            在Google中打开
        </el-button>
        <el-button type="success" size="small" @click="configureToSystemParams" :disabled="!googleSearchQueryWithVariable || !aiUserPrompt">
          一键配置到爬虫参数
        </el-button>
      </el-form-item>
      
      <el-form-item label="程序化搜索谷歌">
        <el-input type="textarea" v-model="googleSearchQueryWithVariable" placeholder="Google搜索语句中的学校网址使用中文变量" :rows="3" readonly class="half-width">
        </el-input>
      </el-form-item>

      <el-form-item label="AI用户提示词">
        <el-input type="textarea" v-model="aiUserPrompt" placeholder="AI用户提示词" :rows="3" readonly class="half-width">
        </el-input>
        <!-- <el-button type="success" size="small" @click="configureToSystemParams" :disabled="!googleSearchQueryWithVariable || !aiUserPrompt">
            一键配置到爬虫参数
        </el-button> -->
      </el-form-item>

    </el-form>
  </el-card>
</template>

<script>
import { schoolList } from '../../api/school'
import { positionList } from '../../api/position'
import { loadToken } from '../../utils/util'

export default {
  name: 'SysParamGoogleDebug',
  data() {
    return {
      // Google搜索调试相关数据
      schoolList: [], // 学校列表
      selectedSchool: null, // 选中的学校
      positionData: [], // 所有职位数据（穿梭框左侧）
      selectedPositions: [], // 选中的职位（穿梭框右侧）
      positionKeywords: '', // 职位关键词输入（保留用于兼容）
      positionORJoinStr: '', // 用OR拼接的关键词
      googleSearchQuery: '', // Google搜索语句
      googleSearchQueryWithVariable: '', // Google搜索语句，其中学校网址使用中文变量
      aiUserPrompt: '', // AI用户提示词
      baseAiUserPrompt: '', // AI用户提示词基础模板
      // 示例职位列表
      examplePositions: [
        'Education Abroad Coordinator', 'global experience director',
        'study abroad director', 'international program director',
        'faculty-led program manager', 'International Student Advisor',
        'Study Abroad Advisor', 'Global Programs Manager'
      ]
    }
  },

  created() {
    // 加载学校列表
    this.loadSchoolList();
    // 加载职位列表
    this.loadPositionList();
    // 初始化AI用户提示词
    this.initAiUserPrompt();
  },

  mounted() {
    // 动态设置穿梭框宽度
    this.$nextTick(() => {
      this.setTransferPanelWidth();
    });
  },

  methods: {
    // 加载学校列表
    loadSchoolList() {
      const params = {
        token: loadToken()
      };

      schoolList(params).then(res => {
        if (res.success && res.data) {
          this.schoolList = res.data;
        } else {
          this.$message.error('获取学校列表失败');
        }
      }).catch(err => {
        this.$message.error('获取学校列表失败');
      });
    },

    // 加载职位列表
    loadPositionList() {
      const params = {
        token: loadToken()
      };

      positionList(params).then(res => {
        if (res.success && res.data) {
          // 转换数据格式为穿梭框需要的格式
          this.positionData = res.data.map(position => ({
            key: position.position,
            label: position.position
          }));
          // 职位数据加载完成后，初始化默认选中
          this.initDefaultPositions();
          // 数据加载完成后设置宽度
          this.$nextTick(() => {
            this.setTransferPanelWidth();
          });
        } else {
          this.$message.error('获取职位列表失败');
        }
      }).catch(err => {
        this.$message.error('获取职位列表失败');
      });
    },

    // 初始化默认选中的职位
    initDefaultPositions() {
      const defaultPositions = [
        'Education Abroad Coordinator',
        'global experience director',
        'study abroad director',
        'international program director',
        'faculty-led program manager'
      ];

      // 设置默认选中的职位（只选择存在于职位数据中的）
      this.selectedPositions = defaultPositions.filter(pos =>
        this.positionData.some(item => item.key === pos)
      );
      // 生成positionORJoinStr
      this.generatePositionORJoinStr();
    },

    // 初始化AI用户提示词
    initAiUserPrompt() {
      // 基础提示词模板
      this.baseAiUserPrompt = `Here is the content:<url_content>\${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.</user_request><schema_block>{  "properties": {    "contactName": {       "description": "The contactName of the entity.",      "title": "contactName",      "type": "string" },    "department": {      "description": "The department of the entity.",      "title": "department",      "type": "string"    },    "position": {      "description": "The position of the  entity.",      "title": "position",      "type": "string"    },    "email": {      "description": "The email of the entity.",      "title": "email",      "type": "string"    },    "phone": {      "description": "The phone of the entity.",      "title": "phone",      "type": "string"    }  },  "required": [    "contactName" ],  "title": "BusinessData",  "type": "object"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using "//" or  "#" in the JSON output. - Do NOT using "\`\`\`json" and "\`\`\`" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.`;

      // 初始化时更新AI用户提示词
      this.updateAiUserPrompt();
    },

    // 更新AI用户提示词
    updateAiUserPrompt() {
      let prompt = this.baseAiUserPrompt;

      // 如果有职位限制，在</user_request>前添加职位限制文本
      if (this.positionORJoinStr) {
        const positionRestriction = `仅抓取以下职位:${this.positionORJoinStr}`;
        prompt = prompt.replace('</user_request>', `${positionRestriction}</user_request>`);
      }

      this.aiUserPrompt = prompt;
    },

    // 学校选择变化处理
    onSchoolChange() {
      this.generateGoogleSearchQuery();
    },

    // 职位选择变化处理
    onPositionSelectionChange() {
      this.generatePositionORJoinStr();
      // 确保宽度设置
      this.$nextTick(() => {
        this.setTransferPanelWidth();
      });
    },

    // 生成positionORJoinStr
    generatePositionORJoinStr() {
      if (this.selectedPositions.length === 0) {
        this.positionORJoinStr = '';
      } else {
        // 生成"职位XXX" OR "职位XXX"格式
        this.positionORJoinStr = this.selectedPositions.map(position => `"${position}"`).join(' OR ');
      }

      // 生成Google搜索语句
      this.generateGoogleSearchQuery();

      // 更新AI用户提示词
      this.updateAiUserPrompt();
    },

    // 职位列表输入变化处理（保留用于兼容）
    onPositionKeywordsChange() {
      this.processPositionKeywords();
    },

    // 从标签添加职位
    addPositionFromTag(position) {
      // 检查是否已选中
      if (!this.selectedPositions.includes(position)) {
        // 检查职位是否在可选列表中
        const positionExists = this.positionData.some(item => item.key === position);
        if (positionExists) {
          // 添加到选中列表
          this.selectedPositions.push(position);
          // 触发变化处理
          this.onPositionSelectionChange();
        } else {
          this.$message.warning(`职位 "${position}" 不在可选列表中`);
        }
      }
    },

    // 处理职位列表
    processPositionKeywords() {
      if (!this.positionKeywords.trim()) {
        this.positionORJoinStr = '';
        this.googleSearchQuery = '';
        this.googleSearchQueryWithVariable = '';
        // 更新AI用户提示词（清空职位限制）
        this.updateAiUserPrompt();
        return;
      }

      // 按中英文逗号分隔，去除空白字符
      const keywords = this.positionKeywords
        .split(/[,，]/)
        .map(keyword => keyword.trim())
        .filter(keyword => keyword.length > 0);

      if (keywords.length === 0) {
        this.positionORJoinStr = '';
        this.googleSearchQuery = '';
        this.googleSearchQueryWithVariable = '';
        // 更新AI用户提示词（清空职位限制）
        this.updateAiUserPrompt();
        return;
      }

      // 生成"职位XXX" OR "职位XXX"格式
      const positionList = keywords.map(keyword => `"${keyword}"`).join(' OR ');
      this.positionORJoinStr = positionList;

      // 生成Google搜索语句
      this.generateGoogleSearchQuery();

      // 更新AI用户提示词
      this.updateAiUserPrompt();
    },

    // 提取域名
    extractDomain(website) {
      if (!website) return '';

      try {
        // 移除协议前缀
        let domain = website.replace(/^https?:\/\//, '');
        // 移除路径
        domain = domain.split('/')[0];
        // 移除www前缀
        domain = domain.replace(/^www\./, '');

        // 如果包含.edu，优先使用.edu域名
        if (domain.includes('.edu')) {
          const parts = domain.split('.');
          const eduIndex = parts.findIndex(part => part === 'edu');
          if (eduIndex > 0) {
            return parts.slice(eduIndex - 1, eduIndex + 1).join('.');
          }
        }

        return domain;
      } catch (error) {
        return website;
      }
    },

    // 生成Google搜索语句
    generateGoogleSearchQuery() {
      if (!this.selectedSchool || !this.selectedSchool.website || !this.positionORJoinStr) {
        this.googleSearchQuery = '';
        this.googleSearchQueryWithVariable = '';
        return;
      }

      const schoolDomain = this.extractDomain(this.selectedSchool.website);
      const template = `site:${schoolDomain} intext:(${this.positionORJoinStr}) (intext:"@${schoolDomain}" OR intext:"email" OR intext:"contact") -inurl:("archive" | "old" | "pdf") -filetype:pdf -filetype:xlsx -filetype:doc`;

      this.googleSearchQuery = template;

      const template2 = `site:学校网址 intext:(${this.positionORJoinStr}) (intext:"@学校网址" OR intext:"email" OR intext:"contact") -inurl:("archive" | "old" | "pdf") -filetype:pdf -filetype:xlsx -filetype:doc`;
      this.googleSearchQueryWithVariable = template2;
    },

    // 一键配置到系统参数
    configureToSystemParams() {
      if (!this.googleSearchQueryWithVariable || !this.aiUserPrompt) {
        this.$message.warning('请先选择学校和输入职位关键词');
        return;
      }

      // 通过事件向父组件传递数据
      this.$emit('configure-to-system-params', {
        googleKeyword: this.googleSearchQueryWithVariable,
        aiUserPrompt: this.aiUserPrompt
      });
    },

    // 在Google中搜索
    openGoogleSearch() {
      if (!this.googleSearchQuery) {
        this.$message.warning('没有可搜索的内容');
        return;
      }

      const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(this.googleSearchQuery)}`;
      window.open(searchUrl, '_blank');
    },

    // 动态设置穿梭框宽度
    setTransferPanelWidth() {
      const panels = document.querySelectorAll('.el-transfer-panel');
      panels.forEach(panel => {
        panel.style.width = '340px';
        panel.style.minWidth = '340px';
        panel.style.maxWidth = 'none';
      });

      // 设置按钮样式
      const buttons = document.querySelectorAll('.el-transfer__button');
      buttons.forEach(button => {
        button.style.width = '25px';
        button.style.height = '25px';
        button.style.padding = '0';
        button.style.margin = '2px 0';
      });

      // 设置按钮容器样式
      const buttonContainers = document.querySelectorAll('.el-transfer__buttons');
      buttonContainers.forEach(container => {
        container.style.padding = '0 10px';
      });
    }
  }
}
</script>

<style scoped>
.half-width {
  width: 43%;
}

.form-item-container {
  display: flex;
  align-items: center;
}

/* 学校选择容器样式 */
.school-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 学校网址样式 */
.school-website {
  color: #909399;
  font-size: 12px;
  white-space: nowrap;
}

/* 穿梭框容器样式 - 居中 */
.transfer-container {
  text-align: center;
  margin: 10px 0;
}

/* 穿梭框容器样式 - 左对齐 */
.transfer-container-left {
  text-align: left;
  margin: 10px 0;
}

/* 使用深度选择器设置穿梭框面板样式 */
/deep/ .el-transfer-panel {
  height: 300px !important;
  width: 340px !important;
  min-width: 340px !important;
  max-width: none !important;
}

/* 或者使用 ::v-deep (Vue 2.7+ 和 Vue 3) */
::v-deep .el-transfer-panel {
  height: 300px !important;
  width: 340px !important;
  min-width: 340px !important;
  max-width: none !important;
}

/* 设置穿梭框面板内容区域的高度 */
/deep/ .el-transfer-panel__body {
  height: calc(300px - 40px) !important;
}

::v-deep .el-transfer-panel__body {
  height: calc(300px - 40px) !important;
}

/* 设置穿梭框列表的高度 */
/deep/ .el-transfer-panel__list {
  height: 100% !important;
  overflow-y: auto !important;
}

::v-deep .el-transfer-panel__list {
  height: 100% !important;
  overflow-y: auto !important;
}

/* 穿梭框样式调整 */
.el-transfer {
  text-align: left;
}

/* 穿梭框按钮样式调整 */
/deep/ .el-transfer__buttons {
  padding: 0 10px !important;
}

::v-deep .el-transfer__buttons {
  padding: 0 10px !important;
}

/* 穿梭框按钮宽度调整 */
/deep/ .el-transfer__button {
  width: 25px !important;
  height: 25px !important;
  padding: 0 !important;
  margin: 2px 0 !important;
}

::v-deep .el-transfer__button {
  width: 25px !important;
  height: 25px !important;
  padding: 0 !important;
  margin: 2px 0 !important;
}

.el-transfer-panel__header {
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.el-transfer-panel__item {
  padding: 6px 12px;
}

.el-transfer-panel__item:hover {
  background-color: #f5f7fa;
}
</style>

<style>
/* 重置下拉框选项的颜色 - 使用更强的选择器和优先级 */
.el-select-dropdown .el-select-dropdown__item,
.el-select-dropdown__item,
.el-popper .el-select-dropdown__item {
  color: #606266 !important;
}

.el-select-dropdown .el-select-dropdown__item:hover,
.el-select-dropdown__item:hover,
.el-popper .el-select-dropdown__item:hover {
  color: #606266 !important;
  background-color: #f5f7fa !important;
}

.el-select-dropdown .el-select-dropdown__item.selected,
.el-select-dropdown__item.selected,
.el-popper .el-select-dropdown__item.selected {
  color: #409eff !important;
  background-color: #f5f7fa !important;
}

/* 额外的覆盖规则，针对可能的其他状态 */
.el-select-dropdown__item span,
.el-select-dropdown__item .el-select-dropdown__item-text {
  color: #606266 !important;
}

.el-select-dropdown__item:hover span,
.el-select-dropdown__item:hover .el-select-dropdown__item-text {
  color: #606266 !important;
}

.el-select-dropdown__item.selected span,
.el-select-dropdown__item.selected .el-select-dropdown__item-text {
  color: #409eff !important;
}

/* Vue 2 中的深度选择器 - 穿梭框宽度 */
/deep/ .el-transfer-panel {
  width: 340px !important;
  min-width: 340px !important;
  max-width: none !important;
}

/* 或者使用 ::v-deep (Vue 2.7+ 和 Vue 3) - 穿梭框宽度 */
::v-deep .el-transfer-panel {
  width: 340px !important;
  min-width: 340px !important;
  max-width: none !important;
}

/* 强制覆盖Element UI默认样式 */
.el-transfer-panel {
  width: 340px !important;
  min-width: 340px !important;
  max-width: none !important;
}

/* 针对自定义类的样式 */
.wide-transfer .el-transfer-panel {
  width: 340px !important;
  min-width: 340px !important;
  max-width: none !important;
}

/* 更具体的选择器 */
.transfer-container-left .wide-transfer .el-transfer-panel {
  width: 340px !important;
  min-width: 340px !important;
  max-width: none !important;
}

/* 强制覆盖穿梭框按钮样式 */
.el-transfer__buttons {
  padding: 0 10px !important;
}

.el-transfer__button {
  width: 25px !important;
  height: 25px !important;
  padding: 0 !important;
  margin: 2px 0 !important;
}

/* 针对自定义类的按钮样式 */
.wide-transfer .el-transfer__buttons {
  padding: 0 10px !important;
}

.wide-transfer .el-transfer__button {
  width: 25px !important;
  height: 25px !important;
  padding: 0 !important;
  margin: 2px 0 !important;
}

</style>
